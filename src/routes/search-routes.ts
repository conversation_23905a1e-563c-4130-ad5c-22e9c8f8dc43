/**
 * Search Routes
 *
 * API routes for semantic search functionality with comprehensive validation.
 */

import { zValidator } from '@hono/zod-validator';
import {
  enforceApiRequestLimits,
  enforcePlanLimits,
  enhancedPermissionCheck,
  type IRBACHonoEnv,
  requireDualAuth,
  teamContextMiddleware,
} from '@middleware/index';
import { searchService } from '@services';
import {
  CollectionInfoRequestSchema,
  type ICollectionInfoResponse,
  type IErrorResponse,
  type ISearchResponse,
  SearchRequestSchema,
} from '@types';
import { ANALYTICS_PERMISSIONS, VECTOR_PERMISSIONS } from '@utils/permission-constants';
import { Hono } from 'hono';

const searchApp = new Hono<IRBACHonoEnv>();

// Apply team context middleware to all routes that need team context for billing
searchApp.use('*', teamContextMiddleware());

// POST /api/search - Perform semantic search
searchApp.post(
  '/',
  requireDualAuth,
  enhancedPermissionCheck({
    requiredPermissions: [VECTOR_PERMISSIONS.READ_VECTORS],
    resourceType: 'search',
    requireAny: true,
    allowInheritance: true,
  }),
  enforceApiRequestLimits(),
  enforcePlanLimits('search_requests'),
  zValidator('json', SearchRequestSchema),
  async (c) => {
    try {
      const request = c.req.valid('json');
      const result = await searchService.performSearch(request);
      return c.json<ISearchResponse>(result);
    } catch (error) {
      console.error('Search endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Search failed',
          error_type: 'search',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// GET /api/search/collections - Get list of available collections
// JUSTIFICATION: This endpoint provides aggregated collection statistics and metadata
// that cannot be efficiently obtained through individual search queries.
// It serves specific use cases:
// 1. UI collection selection dropdowns
// 2. Administrative dashboards showing collection statistics
// 3. API discovery for clients needing to know available collections
// 4. Caching layer for frequently accessed collection metadata
searchApp.get(
  '/collections',
  requireDualAuth,
  enhancedPermissionCheck({
    requiredPermissions: [VECTOR_PERMISSIONS.READ_VECTORS],
    resourceType: 'collection',
    requireAny: true,
    allowInheritance: true,
  }),
  enforceApiRequestLimits(),
  async (c) => {
    try {
      const result = await searchService.getAvailableCollections();
      return c.json({
        status: 'success',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Collections endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to get collections',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// POST /api/search/collection-info - Get detailed information about a collection
// JUSTIFICATION: This endpoint provides detailed collection-specific analytics
// and metadata that would require multiple search queries to aggregate:
// 1. Document count and distribution across categories
// 2. Version information extraction from metadata
// 3. Last updated timestamps across all collection documents
// 4. Structured collection metadata for administrative purposes
// While this could theoretically be replaced by metadata queries,
// it provides significant performance benefits and caching for common operations.
searchApp.post(
  '/collection-info',
  requireDualAuth,
  enhancedPermissionCheck({
    requiredPermissions: [VECTOR_PERMISSIONS.READ_VECTORS],
    resourceType: 'collection',
    requireAny: true,
    allowInheritance: true,
  }),
  enforceApiRequestLimits(),
  zValidator('json', CollectionInfoRequestSchema),
  async (c) => {
    try {
      const request = c.req.valid('json');
      const result = await searchService.getCollectionInfo(request);
      return c.json<ICollectionInfoResponse>(result);
    } catch (error) {
      console.error('Collection info endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to get collection info',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// GET /api/search/stats - Get search statistics
searchApp.get(
  '/stats',
  requireDualAuth,
  enhancedPermissionCheck({
    requiredPermissions: [ANALYTICS_PERMISSIONS.READ_ANALYTICS],
    resourceType: 'analytics',
    requireAny: true,
    allowInheritance: true,
  }),
  enforceApiRequestLimits(),
  async (c) => {
    try {
      const result = await searchService.getSearchStats();
      return c.json({
        status: 'success',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Search stats endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to get search stats',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// GET /api/search/health - Health check for search functionality
searchApp.get('/health', async (c) => {
  try {
    // Perform a simple search to verify functionality
    await searchService.performSearch({
      query: 'test',
      limit: 1,
      returnMetadata: true,
      returnValues: false,
    });

    return c.json({
      status: 'healthy',
      service: 'search',
      checks: {
        vector_database: 'ok',
        embedding_service: 'ok',
        search_functionality: 'ok',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Search health check failed:', error);
    return c.json(
      {
        status: 'unhealthy',
        service: 'search',
        checks: {
          vector_database: 'error',
          embedding_service: 'error',
          search_functionality: 'error',
        },
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

export { searchApp };
