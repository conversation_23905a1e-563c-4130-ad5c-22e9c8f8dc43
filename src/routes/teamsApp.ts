/**
 * Teams Routes
 *
 * REST API endpoints for team management with comprehensive RBAC.
 * Provides team CRUD operations, member management, and permission control.
 */

import { zValidator } from '@hono/zod-validator';
import {
  authMiddleware,
  enforceApiRequestLimits,
  type IUnifiedHonoEnv,
  requireAuth,
  requireMinimumTeamRole,
  requireTeamPermission,
  teamContextMiddleware,
} from '@middleware/index';
import { logTeamAction } from '@services/auditService';
import {
  addTeamMember,
  createTeam,
  deleteTeam,
  getTeamMembers,
  getTeamWithDetails,
  type IAddTeamMemberRequest,
  type ICreateTeamRequest,
  type IUpdateTeamRequest,
  updateTeam,
} from '@services/teamService';
import { Hono } from 'hono';
import { z } from 'zod';
import type { TPermission, TTeamRole } from '@/types/teams';

const app = new Hono<IUnifiedHonoEnv>();

// Apply authentication middleware to all routes
app.use('*', authMiddleware, requireAuth);

// Apply team context middleware to all routes that need team context for billing
app.use('*', teamContextMiddleware());

// ============================================================================
// Validation Schemas
// ============================================================================

const createTeamSchema = z.object({
  name: z.string().min(1).max(255),
  slug: z
    .string()
    .min(1)
    .max(255)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().max(1000).optional(),
  planType: z.enum(['free', 'pro', 'enterprise']).optional(),
});

const updateTeamSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  slug: z
    .string()
    .min(1)
    .max(255)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .optional(),
  description: z.string().max(1000).optional(),
});

const addTeamMemberSchema = z
  .object({
    userId: z.string().uuid().optional(),
    email: z.string().email().optional(),
    role: z.enum(['owner', 'admin', 'manager', 'member', 'viewer']),
    permissions: z.array(z.string()).optional(),
  })
  .refine((data) => data.userId || data.email, {
    message: 'Either userId or email must be provided',
  });

const _updateTeamMemberSchema = z.object({
  role: z.enum(['owner', 'admin', 'manager', 'member', 'viewer']).optional(),
  permissions: z.array(z.string()).optional(),
});

// ============================================================================
// Team CRUD Operations
// ============================================================================

/**
 * POST /api/teams
 * Create a new team
 */
app.post('/', enforceApiRequestLimits(), zValidator('json', createTeamSchema), async (c) => {
  const user = c.get('user');
  const body = c.req.valid('json');

  if (!user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const createParams: ICreateTeamRequest = {
      name: body.name,
      slug: body.slug,
      description: body.description,
      planType: body.planType,
    };

    const team = await createTeam(user.id, createParams);

    // Log the action
    await logTeamAction(c, 'team.create', team.id, {
      team_name: body.name,
      team_slug: body.slug,
      plan_type: body.planType,
    });

    return c.json(
      {
        status: 'success',
        data: {
          team,
        },
        timestamp: new Date().toISOString(),
      },
      201
    );
  } catch (error) {
    console.error('Error creating team:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to create team',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * GET /api/teams/:teamId
 * Get team details
 */
app.get(
  '/:teamId',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('read.team'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const team = await getTeamWithDetails(teamId);

      if (!team) {
        return c.json(
          {
            status: 'error',
            error: 'Team not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      return c.json({
        status: 'success',
        data: {
          team,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching team:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch team',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * PUT /api/teams/:teamId
 * Update team information
 */
app.put(
  '/:teamId',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('manage.team'),
  zValidator('json', updateTeamSchema),
  async (c) => {
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    try {
      const updateParams: IUpdateTeamRequest = {
        name: body.name,
        slug: body.slug,
        description: body.description,
      };

      const updatedTeam = await updateTeam(teamId, updateParams);

      if (!updatedTeam) {
        return c.json(
          {
            status: 'error',
            error: 'Team not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      // Log the action
      await logTeamAction(c, 'team.update', teamId, {
        old_values: {
          name: updatedTeam.name,
          slug: updatedTeam.slug,
        },
        new_values: body,
      });

      return c.json({
        status: 'success',
        data: {
          team: updatedTeam,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error updating team:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to update team',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * DELETE /api/teams/:teamId
 * Delete team
 */
app.delete(
  '/:teamId',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireMinimumTeamRole('owner'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const deleted = await deleteTeam(teamId);

      if (!deleted) {
        return c.json(
          {
            status: 'error',
            error: 'Team not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      // Log the action
      await logTeamAction(c, 'team.delete', teamId, {
        team_id: teamId,
      });

      return c.json({
        status: 'success',
        data: {
          message: 'Team deleted successfully',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error deleting team:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to delete team',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Team Member Management
// ============================================================================

/**
 * GET /api/teams/:teamId/members
 * Get team members
 */
app.get(
  '/:teamId/members',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('read.team'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const members = await getTeamMembers(teamId);

      return c.json({
        status: 'success',
        data: {
          members,
          total: members.length,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching team members:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch team members',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * POST /api/teams/:teamId/members
 * Add team member
 */
app.post(
  '/:teamId/members',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('manage.team'),
  zValidator('json', addTeamMemberSchema),
  async (c) => {
    const user = c.get('user');
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    if (!user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    try {
      const addParams: IAddTeamMemberRequest = {
        userId: body.userId,
        email: body.email,
        role: body.role as TTeamRole,
        permissions: body.permissions as TPermission[],
      };

      const member = await addTeamMember(teamId, addParams, user.id);

      if (!member) {
        return c.json(
          {
            status: 'error',
            error: 'Failed to add team member',
            error_type: 'database',
            timestamp: new Date().toISOString(),
          },
          500
        );
      }

      // Log the action
      await logTeamAction(c, 'team_member.add', teamId, {
        member_id: member.id,
        member_email: member.user.email,
        role: body.role,
        permissions: body.permissions,
      });

      return c.json(
        {
          status: 'success',
          data: {
            member,
          },
          timestamp: new Date().toISOString(),
        },
        201
      );
    } catch (error) {
      console.error('Error adding team member:', error);
      return c.json(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to add team member',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

export default app;
