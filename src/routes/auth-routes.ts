/**
 * Authentication Routes
 *
 * Better Auth integration routes for Hono.
 * Handles authentication endpoints and session management.
 */

import { authMiddleware, type IAuthHonoEnv } from '@middleware/auth-middleware';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { getAuth } from '../auth';

const authApp = new Hono<IAuthHonoEnv>();

// CORS configuration for auth routes
authApp.use(
  '*',
  cors({
    origin: ['http://localhost:3000', 'https://localhost:3000', 'http://localhost:8787'],
    allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    exposeHeaders: ['Set-Cookie'],
    maxAge: 600,
    credentials: true,
  })
);

// Apply auth middleware to get session info
authApp.use('*', authMiddleware);

/**
 * Mount Better Auth handler
 *
 * Handles all Better Auth API endpoints:
 * - POST /api/auth/sign-in/email
 * - POST /api/auth/sign-up/email
 * - POST /api/auth/sign-out
 * - GET /api/auth/session
 * - And other Better Auth endpoints
 */
authApp.on(['POST', 'GET'], '/*', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Authentication service error',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * Get current session endpoint
 *
 * Returns the current user session information.
 * This is a convenience endpoint that uses the middleware session.
 */
authApp.get('/session', (c) => {
  const user = c.get('user');
  const session = c.get('session');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user || !session) {
    return c.json(
      {
        status: 'error',
        error: 'No active session',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  return c.json({
    status: 'success',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      session: {
        id: session.id,
        expiresAt: session.expiresAt,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
      },
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * Get user profile endpoint
 *
 * Returns the current user's profile information.
 */
authApp.get('/me', (c) => {
  const user = c.get('user');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  return c.json({
    status: 'success',
    data: {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
    timestamp: new Date().toISOString(),
  });
});

/**
 * Health check for auth service
 */
authApp.get('/health', async (c) => {
  try {
    const _auth = await getAuth();

    return c.json({
      status: 'success',
      service: 'Better Auth',
      version: '1.0.0',
      healthy: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Auth health check error:', error);

    return c.json(
      {
        status: 'error',
        service: 'Better Auth',
        healthy: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

/**
 * List active sessions endpoint
 *
 * Returns all active sessions for the current user.
 */
authApp.get('/sessions', async (c) => {
  const user = c.get('user');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const _auth = await getAuth();

    // Note: This would require implementing a custom endpoint in Better Auth
    // For now, return the current session
    const session = c.get('session');

    return c.json({
      status: 'success',
      data: {
        sessions: session ? [session] : [],
        total: session ? 1 : 0,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('List sessions error:', error);

    return c.json(
      {
        status: 'error',
        error: 'Failed to retrieve sessions',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * Revoke session endpoint
 *
 * Revokes a specific session by token.
 */
authApp.delete('/sessions/:token', async (c) => {
  const user = c.get('user');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  const token = c.req.param('token');

  if (!token) {
    return c.json(
      {
        status: 'error',
        error: 'Session token is required',
        error_type: 'validation',
        timestamp: new Date().toISOString(),
      },
      400
    );
  }

  try {
    const _auth = await getAuth();

    // Note: This would require implementing session revocation in Better Auth
    // For now, return success

    return c.json({
      status: 'success',
      message: 'Session revoked successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Revoke session error:', error);

    return c.json(
      {
        status: 'error',
        error: 'Failed to revoke session',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export { authApp };
