/**
 * Billing API Routes
 *
 * Comprehensive billing API endpoints for subscription management,
 * payment processing, and billing operations with Paddle and PayPal.
 */

import { zValidator } from '@hono/zod-validator';
import {
  addBillingContext,
  authMiddleware,
  billingRateLimit,
  enforceApiRequestLimits,
  type IUnifiedHonoEnv,
  requireAuth,
  requireTeamPermission,
  teamContextMiddleware,
  validatePaymentAmount,
  verifyWebhookSignature,
} from '@middleware/index';
import type { TAuditAction, TAuditResource } from '@services/auditService';
import { createAuditLog } from '@services/auditService';
import { billingService } from '@services/billingService';
import { paddleService } from '@services/paddleService';
import { paypalService } from '@services/paypalService';
import type { Context, Next } from 'hono';
import { Hono } from 'hono';
import { z } from 'zod';
import type { TBillingInterval, TSubscriptionPlan } from '@/types/billing';
import type { TPermission } from '@/types/teams';

const app = new Hono<IUnifiedHonoEnv>();

// ============================================================================
// Type-Compatible Middleware Wrappers
// ============================================================================

/**
 * Type-compatible wrapper for requireTeamPermission middleware
 */
const billingRequireTeamPermission = (requiredPermission: TPermission) => {
  return async (c: Context<IUnifiedHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team context required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    const { hasPermission } = await import('@utils/permissions');
    const permissionCheck = hasPermission(teamContext, requiredPermission);

    if (!permissionCheck.allowed) {
      return c.json(
        {
          status: 'error',
          error: permissionCheck.reason || 'Insufficient permissions',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

/**
 * Type-compatible wrapper for createAuditLogFromContext
 */
const createBillingAuditLog = async (
  c: Context<IUnifiedHonoEnv>,
  action: TAuditAction,
  resource: TAuditResource,
  resourceId?: string,
  details?: Record<string, unknown>
) => {
  const user = c.get('user');
  const teamId = c.get('teamId');
  const ipAddress = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || null;
  const userAgent = c.req.header('User-Agent') || null;

  return createAuditLog({
    userId: user?.id,
    teamId: teamId || undefined,
    action,
    resource,
    resourceId,
    details: {
      ...details,
      ip_address: ipAddress || undefined,
      user_agent: userAgent || undefined,
    },
    ipAddress: ipAddress || undefined,
    userAgent: userAgent || undefined,
  });
};

// Apply authentication and rate limiting to all routes
app.use('*', authMiddleware, billingRateLimit);

// ============================================================================
// Validation Schemas
// ============================================================================

const createSubscriptionSchema = z.object({
  planId: z.enum(['starter', 'professional', 'enterprise']),
  interval: z.enum(['monthly', 'yearly']),
  provider: z.enum(['paddle', 'paypal']),
  paymentMethodId: z.string().optional(),
  couponCode: z.string().optional(),
});

const updateSubscriptionSchema = z.object({
  planId: z.enum(['starter', 'professional', 'enterprise']).optional(),
  interval: z.enum(['monthly', 'yearly']).optional(),
  paymentMethodId: z.string().optional(),
});

const createPaymentSchema = z.object({
  amount: z.number().int().positive(),
  currency: z.string().length(3),
  description: z.string().min(1).max(255),
  provider: z.enum(['paddle', 'paypal']),
});

// ============================================================================
// Public Routes (Plans and Pricing)
// ============================================================================

/**
 * GET /api/billing/plans
 * Get all available subscription plans
 */
app.get('/plans', enforceApiRequestLimits(), async (c) => {
  try {
    const plans = billingService.getAvailablePlans();

    return c.json({
      status: 'success',
      data: {
        plans: plans.map((plan) => ({
          id: plan.id,
          name: plan.name,
          description: plan.description,
          features: plan.features,
          pricing: plan.pricing,
          popular: plan.popular,
          trialDays: plan.trialDays,
        })),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching billing plans:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to fetch billing plans',
        error_type: 'billing_error',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// ============================================================================
// Team Billing Routes
// ============================================================================

// Apply team context and billing context to team routes
app.use('/teams/:teamId/*', requireAuth, teamContextMiddleware(), addBillingContext);

/**
 * GET /api/billing/teams/:teamId/dashboard
 * Get billing dashboard data for a team
 */
app.get(
  '/teams/:teamId/dashboard',
  enforceApiRequestLimits(),
  requireTeamPermission('read.billing'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const dashboard = await billingService.getBillingDashboard(teamId);

      await createBillingAuditLog(c, 'billing.payment_success', 'billing_subscription', teamId);

      return c.json({
        status: 'success',
        data: { dashboard },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching billing dashboard:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch billing dashboard',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * GET /api/billing/teams/:teamId/usage
 * Get usage data for a team
 */
app.get(
  '/teams/:teamId/usage',
  enforceApiRequestLimits(),
  requireTeamPermission('read.billing'),
  async (c) => {
    const teamId = c.req.param('teamId');
    const startDate = c.req.query('start_date');
    const endDate = c.req.query('end_date');

    try {
      const usage = await billingService.getUsageData(
        teamId,
        startDate ? new Date(startDate) : undefined,
        endDate ? new Date(endDate) : undefined
      );

      return c.json({
        status: 'success',
        data: { usage },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching usage data:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch usage data',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * POST /api/billing/teams/:teamId/subscriptions
 * Create a new subscription for a team
 */
app.post(
  '/teams/:teamId/subscriptions',
  enforceApiRequestLimits(),
  billingRequireTeamPermission('manage.billing'),
  zValidator('json', createSubscriptionSchema),
  async (c) => {
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    try {
      interface ISubscriptionResult {
        localSubscription: {
          id: string;
          teamId: string;
          planId: string;
          status: string;
        };
        paypalSubscription?: {
          id: string;
          status: string;
          plan_id: string;
          links?: Array<{ rel: string; href: string }>;
        };
        paddleSubscription?: {
          id: string;
          status: string;
          items: Array<{ priceId: string; quantity: number }>;
          checkout_url?: string;
        };
      }

      let result: ISubscriptionResult;

      if (body.provider === 'paddle') {
        result = await paddleService.createSubscription({
          teamId,
          planId: body.planId as TSubscriptionPlan,
          interval: body.interval as TBillingInterval,
          provider: 'paddle',
          paymentMethodId: body.paymentMethodId,
        });
      } else {
        result = await paypalService.createSubscription({
          teamId,
          planId: body.planId as TSubscriptionPlan,
          interval: body.interval as TBillingInterval,
          provider: 'paypal',
          paymentMethodId: body.paymentMethodId,
        });
      }

      await createBillingAuditLog(
        c,
        'billing.subscription_create',
        'billing_subscription',
        result.localSubscription.id,
        {
          provider: body.provider,
          planId: body.planId,
          interval: body.interval,
        }
      );

      return c.json({
        status: 'success',
        data: {
          subscription: result.localSubscription,
          checkoutUrl:
            result.paddleSubscription?.checkout_url ||
            result.paypalSubscription?.links?.find(
              (l: { rel: string; href: string }) => l.rel === 'approve'
            )?.href,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error creating subscription:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create subscription',
          error_type: 'billing_error',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * PUT /api/billing/teams/:teamId/subscriptions/:subscriptionId
 * Update an existing subscription
 */
app.put(
  '/teams/:teamId/subscriptions/:subscriptionId',
  billingRequireTeamPermission('manage.billing'),
  zValidator('json', updateSubscriptionSchema),
  async (c) => {
    const subscriptionId = c.req.param('subscriptionId');
    const body = c.req.valid('json');

    try {
      // Get current subscription to determine provider
      const currentSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!currentSubscription) {
        return c.json(
          {
            status: 'error',
            error: 'Subscription not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      interface IUpdatedSubscription {
        id: string;
        teamId: string;
        planId: string;
        status: string;
      }

      let updatedSubscription: IUpdatedSubscription;

      if (currentSubscription.provider === 'paddle') {
        updatedSubscription = await paddleService.updateSubscription(subscriptionId, body);
      } else {
        updatedSubscription = await paypalService.updateSubscription(subscriptionId, body);
      }

      await createBillingAuditLog(
        c,
        'billing.subscription_update',
        'billing_subscription',
        subscriptionId,
        {
          provider: currentSubscription.provider,
          changes: body,
        }
      );

      return c.json({
        status: 'success',
        data: { subscription: updatedSubscription },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error updating subscription:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to update subscription',
          error_type: 'billing_error',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * DELETE /api/billing/teams/:teamId/subscriptions/:subscriptionId
 * Cancel a subscription
 */
app.delete(
  '/teams/:teamId/subscriptions/:subscriptionId',
  billingRequireTeamPermission('manage.billing'),
  async (c) => {
    const subscriptionId = c.req.param('subscriptionId');

    try {
      // Get current subscription to determine provider
      const currentSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!currentSubscription) {
        return c.json(
          {
            status: 'error',
            error: 'Subscription not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      if (currentSubscription.provider === 'paddle') {
        await paddleService.cancelSubscription(subscriptionId);
      } else {
        await paypalService.cancelSubscription(subscriptionId);
      }

      await createBillingAuditLog(
        c,
        'billing.subscription_cancel',
        'billing_subscription',
        subscriptionId,
        {
          provider: currentSubscription.provider,
        }
      );

      return c.json({
        status: 'success',
        data: { subscription: { id: subscriptionId, status: 'canceled' } },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to cancel subscription',
          error_type: 'billing_error',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Payment Routes
// ============================================================================

/**
 * POST /api/billing/teams/:teamId/payments
 * Create a one-time payment
 */
app.post(
  '/teams/:teamId/payments',
  billingRequireTeamPermission('manage.billing'),
  zValidator('json', createPaymentSchema),
  validatePaymentAmount,
  async (c) => {
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    try {
      interface IPaymentResult {
        id: string;
        status: string;
        amount: {
          currency_code: string;
          value: string;
        };
        links?: Array<{ rel: string; href: string }>;
      }

      let result: IPaymentResult;

      if (body.provider === 'paypal') {
        result = await paypalService.createPayment({
          teamId,
          amount: body.amount,
          currency: body.currency,
          description: body.description,
        });
      } else {
        throw new Error('One-time payments only supported with PayPal');
      }

      await createBillingAuditLog(c, 'billing.payment_success', 'billing_transaction', result.id, {
        provider: body.provider,
        amount: body.amount,
        currency: body.currency,
      });

      return c.json({
        status: 'success',
        data: {
          payment: result,
          approvalUrl: result.links?.find((l: { rel: string; href: string }) => l.rel === 'approve')
            ?.href,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error creating payment:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create payment',
          error_type: 'billing_error',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Additional API Requests Routes
// ============================================================================

/**
 * Validation schema for purchasing additional API requests
 */
const purchaseApiRequestsSchema = z.object({
  requestCount: z.number().min(1).max(100), // 1-100 thousand requests
  provider: z.enum(['paddle', 'paypal']),
  paymentMethodId: z.string().optional(),
});

/**
 * POST /api/billing/teams/:teamId/api-requests
 * Purchase additional API request packages
 */
app.post(
  '/teams/:teamId/api-requests',
  enforceApiRequestLimits(),
  billingRequireTeamPermission('manage.billing'),
  zValidator('json', purchaseApiRequestsSchema),
  async (c) => {
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    try {
      const result = await billingService.purchaseAdditionalApiRequests({
        teamId,
        requestCount: body.requestCount,
        provider: body.provider,
        paymentMethodId: body.paymentMethodId,
      });

      await createBillingAuditLog(
        c,
        'billing.api_requests_purchase',
        'billing_transaction',
        result.transaction.id,
        {
          provider: body.provider,
          requestCount: body.requestCount,
          amount: result.transaction.amount,
        }
      );

      return c.json({
        status: 'success',
        data: {
          transaction: result.transaction,
          additionalRequests: result.additionalRequests,
          message: `Successfully purchased ${body.requestCount * 1000} additional API requests`,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error purchasing additional API requests:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to purchase additional API requests',
          error_type: 'billing_error',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * GET /api/billing/teams/:teamId/api-requests
 * Get additional API requests for a team
 */
app.get(
  '/teams/:teamId/api-requests',
  enforceApiRequestLimits(),
  billingRequireTeamPermission('read.billing'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const additionalRequests = await billingService.getAdditionalApiRequests(teamId);

      return c.json({
        status: 'success',
        data: { additionalRequests },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching additional API requests:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch additional API requests',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * GET /api/billing/teams/:teamId/api-requests/history
 * Get purchase history for additional API request packages
 */
app.get(
  '/teams/:teamId/api-requests/history',
  enforceApiRequestLimits(),
  billingRequireTeamPermission('read.billing'),
  async (c) => {
    const teamId = c.req.param('teamId');
    const limit = Number(c.req.query('limit')) || 50;
    const offset = Number(c.req.query('offset')) || 0;

    // Validate pagination parameters
    if (limit < 1 || limit > 100) {
      return c.json(
        {
          status: 'error',
          error: 'Limit must be between 1 and 100',
          error_type: 'validation_error',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    if (offset < 0) {
      return c.json(
        {
          status: 'error',
          error: 'Offset must be non-negative',
          error_type: 'validation_error',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    try {
      const history = await billingService.getAdditionalApiRequestsPurchaseHistory(
        teamId,
        limit,
        offset
      );

      return c.json({
        status: 'success',
        data: {
          purchases: history.purchases,
          pagination: {
            total: history.total,
            limit,
            offset,
            hasMore: history.hasMore,
          },
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching additional API requests purchase history:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch purchase history',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * GET /api/billing/teams/:teamId/api-requests/balance
 * Get current additional API request balance for a team
 */
app.get(
  '/teams/:teamId/api-requests/balance',
  enforceApiRequestLimits(),
  billingRequireTeamPermission('read.billing'),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const balance = await billingService.getAdditionalApiRequestBalance(teamId);

      return c.json({
        status: 'success',
        data: { balance },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching additional API request balance:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch balance',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Webhook Routes
// ============================================================================

/**
 * POST /api/billing/webhooks/paddle
 * Handle Paddle webhook events
 */
app.post('/webhooks/paddle', verifyWebhookSignature('paddle'), async (c) => {
  try {
    const body = c.get('webhookBody') as string;
    const event = JSON.parse(body);

    await paddleService.handleWebhook({
      id: event.alert_id,
      type: event.alert_name,
      provider: 'paddle',
      data: event,
      timestamp: new Date(),
    });

    return c.json({ status: 'success' });
  } catch (error) {
    console.error('Paddle webhook processing failed:', error);
    return c.json(
      {
        status: 'error',
        error: 'Webhook processing failed',
        error_type: 'webhook_error',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * POST /api/billing/webhooks/paypal
 * Handle PayPal webhook events
 */
app.post('/webhooks/paypal', verifyWebhookSignature('paypal'), async (c) => {
  try {
    const body = c.get('webhookBody') as string;
    const event = JSON.parse(body);

    await paypalService.handleWebhook({
      id: event.id,
      type: event.event_type,
      provider: 'paypal',
      data: event,
      timestamp: new Date(event.create_time),
    });

    return c.json({ status: 'success' });
  } catch (error) {
    console.error('PayPal webhook processing failed:', error);
    return c.json(
      {
        status: 'error',
        error: 'Webhook processing failed',
        error_type: 'webhook_error',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export default app;
