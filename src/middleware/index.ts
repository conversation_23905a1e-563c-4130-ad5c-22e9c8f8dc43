/**
 * Middleware Index
 *
 * Central export point for all middleware functions.
 */

// API Key middleware
export {
  apiKeyMiddleware,
  type IApiKeyHonoEnv,
  requireApiKey,
  requireDualAuth,
} from './api-key-middleware';
// API Key permissions middleware (unified with RBAC system)
export {
  API_KEY_PERMISSION_GROUPS,
  API_KEY_PERMISSION_MAPPING,
  requireAllPermissions,
  requireAnyPermission,
  requirePermission,
} from './api-key-permissions';
// Authentication middleware
export {
  authMiddleware,
  type IAuthHonoEnv,
  optionalAuth,
  requireAuth,
  requireOwnership,
  requireRoles,
} from './auth-middleware';
// Billing middleware
export {
  addBillingContext,
  billingRateLimit,
  enforceApiRequestLimits,
  enforcePlanLimits,
  type IBillingHonoEnv,
  requireActiveSubscription,
  requireFeature,
  validatePaymentAmount,
  verifyWebhookSignature,
} from './billing-middleware';
// Error handling middleware
export { errorHandler } from './error-handler';
export {
  apiRequestSanitizer,
  createApiRequestSanitizer,
  createSearchQuerySanitizer,
  createTextContentSanitizer,
  InputSanitizer,
  sanitizeFilename,
  searchQuerySanitizer,
  textContentSanitizer,
  validateCategory,
  validateContentHash,
  validateEmbeddingProvider,
  validateLibraryName,
  validateProjectID,
  validateTenantID,
  validateUserID,
} from './input-sanitizer';
// Permission-based rate limiting middleware
export {
  adaptivePermissionRateLimit,
  getApiKeyRateLimitStatus,
  type IPermissionRateLimit,
  type IPermissionRateLimitHonoEnv,
  type IRateLimitResult,
  PERMISSION_RATE_LIMITS,
  permissionRateLimit,
} from './permission-rate-limiting';
export { productionLogger, requestIdMiddleware, securityHeaders } from './production-logger';
export {
  apiRateLimit,
  createApiRateLimiter,
  createSearchRateLimiter,
  createUploadRateLimiter,
  RateLimiter,
  searchRateLimit,
  uploadRateLimit,
} from './rate-limiter';
// RBAC middleware
export {
  adminOnlyPermissionCheck,
  enhancedPermissionCheck,
  type IRBACHonoEnv,
  requireAllTeamPermissions,
  requireAnyTeamPermission,
  requireMinimumTeamRole,
  requireTeamMembership,
  requireTeamPermission,
  resourcePermissionCheck,
  teamContextMiddleware,
} from './rbac-middleware';

// ============================================================================
// Unified Environment Types
// ============================================================================

import type { IBillingHonoEnv } from './billing-middleware';
import type { IRBACHonoEnv } from './rbac-middleware';

/**
 * Unified Hono environment that combines RBAC and billing contexts
 *
 * This type provides access to all authentication, authorization, team context,
 * and billing-related variables in a single environment type.
 */
export interface IUnifiedHonoEnv extends IRBACHonoEnv {
  Variables: IRBACHonoEnv['Variables'] & IBillingHonoEnv['Variables'];
}
