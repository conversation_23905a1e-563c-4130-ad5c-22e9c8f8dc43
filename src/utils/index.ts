// Re-export core utilities from dedicated files to avoid service import cascade
export { getCtx, getEnv, getConfig, type IAppConfig } from './env';
export { getAi, getKV, getR2, getVDB, getBrowser, getOpenAI, getVoyageAI, getDatabase, getDB } from './database';

// Export services for backward compatibility - TEMPORARILY DISABLED TO AVOID GLOBAL SCOPE ASYNC OPERATIONS
// export {
//   ChunkingService,
//   CodeDetectionService,
//   chunkingService,
//   codeDetectionService,
//   EmbeddingService,
//   embeddingService,
// } from '@services';
// Export types
export type {
  IBaseChunkMetadata,
  IChunkingConfig,
  ICodeDetectionResult,
  IEmbeddingConfig,
  IEmbeddingResult,
  ITextChunk,
  TEmbeddingProvider,
} from '@types';

// Export document configuration utilities
export * from './document-config';
