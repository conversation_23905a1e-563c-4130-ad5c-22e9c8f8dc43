/**
 * Database Utilities
 *
 * Dedicated utilities for database and AI service access.
 * Separated from main utils to avoid service import cascade issues.
 */

import { OpenAI } from 'openai';
import { VoyageAIClient } from 'voyageai';
import getDB from './getDB';
import { getEnv } from './env';

/**
 * Gets the Cloudflare AI binding for AI model inference
 * @returns Promise resolving to the AI binding
 */
export const getAi = async () => {
  return (await getEnv()).ai;
};

/**
 * Gets the Cloudflare KV binding for key-value storage
 * @returns Promise resolving to the KV binding
 */
export const getKV = async () => {
  return (await getEnv()).kv;
};

/**
 * Gets the Cloudflare R2 binding for object storage
 * @returns Promise resolving to the R2 binding
 */
export const getR2 = async () => {
  return (await getEnv()).r2;
};

/**
 * Gets the Cloudflare Vectorize binding for vector database operations
 * @returns Promise resolving to the Vectorize binding
 */
export const getVDB = async () => {
  return (await getEnv()).vdb;
};

/**
 * Gets the Cloudflare Browser binding for browser automation
 * @returns Promise resolving to the Browser binding
 */
export const getBrowser = async () => {
  return (await getEnv()).browser;
};

/**
 * Gets a configured OpenAI client instance
 *
 * @returns Promise resolving to configured OpenAI client
 * @throws Error if no API key is configured
 *
 * @example
 * ```typescript
 * const openai = await getOpenAI();
 * const response = await openai.embeddings.create({
 *   model: 'text-embedding-3-large',
 *   input: 'Hello world'
 * });
 * ```
 */
export const getOpenAI = async () => {
  const env = await getEnv();
  const apiKey = env.OPENAI_API_KEY || env.OPENROUTER_API_KEY;
  const baseURL = env.OPENAI_BASE_URL;

  if (!apiKey) {
    throw new Error('OpenAI API key not configured. Set OPENAI_API_KEY or OPENROUTER_API_KEY environment variable.');
  }

  return new OpenAI({
    apiKey,
    baseURL,
  });
};

/**
 * Gets a VoyageAI client instance with proper configuration
 *
 * @returns Promise resolving to configured VoyageAI client
 * @throws Error if no API key is configured
 *
 * @example
 * ```typescript
 * const voyage = await getVoyageAI();
 * const response = await voyage.embed({
 *   model: 'voyage-3-large',
 *   input: 'Hello world'
 * });
 * ```
 */
export const getVoyageAI = async () => {
  const env = await getEnv();
  const apiKey = env.VOYAGEAI_API_KEY;

  if (!apiKey) {
    throw new Error('VoyageAI API key not configured. Set VOYAGEAI_API_KEY environment variable.');
  }

  return new VoyageAIClient({
    apiKey,
  });
};

/**
 * Gets the database connection
 * @returns Promise resolving to the database connection
 */
export const getDatabase = getDB;

// Re-export getDB for backward compatibility
export { default as getDB } from './getDB';
