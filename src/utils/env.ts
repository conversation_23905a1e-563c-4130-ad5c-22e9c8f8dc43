/**
 * Environment Utilities
 *
 * Dedicated utilities for environment variable access and configuration.
 * Separated from main utils to avoid service import cascade issues.
 */

import type { ICloudflareEnv } from '@types';
import { getContext } from 'hono/context-storage';

/**
 * Gets the current Hono context
 * @returns Promise resolving to the current execution context
 */
export const getCtx = async () => {
  return getContext<Env>();
};

/**
 * Gets the Cloudflare Workers environment bindings
 * @returns Promise resolving to environment variables and bindings
 */
export const getEnv = async () => {
  return (await getCtx()).env;
};

/**
 * Application configuration interface
 */
export interface IAppConfig {
  // Core settings
  environment: 'development' | 'staging' | 'production';

  // Security settings
  rateLimitEnabled: boolean;
  requestsPerMinute: number;
  corsEnabled: boolean;

  // File upload settings
  maxFileSize: number;
  allowedFileExtensions: string[];

  // Embedding settings
  defaultEmbeddingProvider: 'openai' | 'voyageai' | 'cloudflare';
  defaultEmbeddingModel: string;
  vectorDimensions: number;

  // MCP settings
  mcpMaxConnections: number;
  mcpEnabled: boolean;
}

/**
 * Gets the application configuration from environment variables
 *
 * @returns Promise resolving to complete application configuration
 *
 * @example
 * ```typescript
 * const config = await getConfig();
 * console.log(`Rate limiting: ${config.rateLimitEnabled}`);
 * console.log(`Max file size: ${config.maxFileSize} bytes`);
 * console.log(`Default provider: ${config.defaultEmbeddingProvider}`);
 * ```
 */
export async function getConfig(): Promise<IAppConfig> {
  let env: Partial<ICloudflareEnv> = {};

  try {
    env = await getEnv();
  } catch (_error) {
    // In test environment or when context is not available, use defaults
    env = {};
  }

  return {
    // Determine environment (default to development for Cloudflare Workers)
    environment: 'development',

    // Security settings
    rateLimitEnabled: env.RATE_LIMIT_ENABLED !== 'false',
    requestsPerMinute: parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE || '60'),
    corsEnabled: true,

    // File upload settings
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileExtensions: env.ALLOWED_FILE_EXTENSIONS
      ? env.ALLOWED_FILE_EXTENSIONS.split(',').map((ext) => ext.trim())
      : [
          '.txt',
          '.md',
          '.json',
          '.pdf',
          '.doc',
          '.docx',
          '.py',
          '.js',
          '.ts',
          '.jsx',
          '.tsx',
          '.java',
          '.cpp',
          '.c',
          '.h',
          '.cs',
          '.php',
          '.rb',
          '.go',
          '.rs',
          '.swift',
          '.kt',
          '.scala',
          '.r',
          '.sql',
          '.sh',
          '.bat',
          '.ps1',
          '.yaml',
          '.yml',
          '.xml',
          '.html',
          '.css',
          '.scss',
          '.less',
        ],

    // Embedding settings
    defaultEmbeddingProvider: (env.EMBEDDING_PROVIDER as 'openai' | 'voyageai' | 'cloudflare') || 'cloudflare',
    defaultEmbeddingModel: env.EMBEDDING_MODEL || '@cf/baai/bge-large-en-v1.5',
    vectorDimensions: parseInt(env.VECTOR_DIMENSIONS || '1024'),

    // MCP settings
    mcpMaxConnections: parseInt(env.MCP_MAX_CONNECTIONS || '100'),
    mcpEnabled: true,
  };
}
