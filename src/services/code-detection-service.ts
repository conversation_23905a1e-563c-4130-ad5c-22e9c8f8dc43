/**
 * Code Detection Service
 *
 * Provides intelligent code content detection for automatic categorization.
 * Used to determine if uploaded content should be treated as code or documentation.
 */

import type { ICodeDetectionResult } from '@types';

// File extensions that are definitely code
const CODE_EXTENSIONS = [
  '.py',
  '.js',
  '.ts',
  '.jsx',
  '.tsx',
  '.java',
  '.cpp',
  '.c',
  '.h',
  '.cs',
  '.php',
  '.rb',
  '.go',
  '.rs',
  '.swift',
  '.kt',
  '.scala',
  '.r',
  '.sql',
  '.sh',
  '.bat',
  '.ps1',
  '.yaml',
  '.yml',
  '.xml',
  '.html',
  '.css',
  '.scss',
  '.less',
];

// File extensions that could be either code or docs
const AMBIGUOUS_EXTENSIONS = ['.txt', '.md', '.json'];

// Code patterns to look for in content
const CODE_PATTERNS = [
  // Function definitions
  /function\s+\w+\s*\(/i,
  /def\s+\w+\s*\(/i,
  /public\s+\w+\s+\w+\s*\(/i,
  /private\s+\w+\s+\w+\s*\(/i,
  /const\s+\w+\s*=\s*\(/i,
  /let\s+\w+\s*=\s*\(/i,
  /var\s+\w+\s*=\s*\(/i,

  // Class definitions
  /class\s+\w+/i,
  /interface\s+\w+/i,
  /struct\s+\w+/i,
  /enum\s+\w+/i,

  // Import/include statements
  /import\s+.*from/i,
  /import\s+\w+/i,
  /from\s+\w+\s+import/i,
  /#include\s*</i,
  /require\s*\(/i,
  /using\s+\w+/i,

  // Control structures
  /if\s*\([^)]*\)\s*{/i,
  /for\s*\([^)]*\)\s*{/i,
  /while\s*\([^)]*\)\s*{/i,
  /switch\s*\([^)]*\)\s*{/i,
  /try\s*{/i,
  /catch\s*\([^)]*\)\s*{/i,

  // Variable declarations with types
  /\w+\s*:\s*\w+\s*=/i,
  /\w+\s+\w+\s*=/i,

  // Common code symbols
  /=>\s*{/i,
  /\w+\.\w+\(/i,
  /console\.log\(/i,
  /print\(/i,
  /System\.out\.println/i,
  /std::/i,

  // SQL patterns
  /SELECT\s+.*FROM/i,
  /INSERT\s+INTO/i,
  /UPDATE\s+.*SET/i,
  /DELETE\s+FROM/i,
  /CREATE\s+TABLE/i,

  // Configuration patterns (YAML, JSON-like)
  /^\s*\w+:\s*$/m,
  /^\s*-\s+\w+:/m,
  /^\s*{\s*$/m,
  /^\s*}\s*$/m,
  /^\s*\[\s*$/m,
  /^\s*\]\s*$/m,

  // Shell script patterns
  /^#!/,
  /\$\w+/,
  /echo\s+/i,
  /export\s+\w+=/i,

  // HTML/XML patterns
  /<\w+[^>]*>/,
  /<\/\w+>/,
  /<!DOCTYPE/i,

  // CSS patterns
  /\w+\s*{\s*$/m,
  /\w+:\s*[^;]+;/,
  /@media\s+/i,
  /@import\s+/i,
];

// Documentation patterns that suggest it's NOT code
const DOCUMENTATION_PATTERNS = [
  // Natural language indicators
  /^#\s+[A-Z][a-z]/m, // Markdown headers with natural language
  /\b(the|and|or|but|however|therefore|because|although|while|during|after|before|since|until|unless|if|when|where|why|how|what|who|which|that)\b/gi,

  // Documentation-specific patterns
  /^##\s+/m, // Markdown subheaders
  /^\*\s+/m, // Bullet points
  /^\d+\.\s+/m, // Numbered lists
  /\[.*\]\(.*\)/, // Markdown links
  /!\[.*\]\(.*\)/, // Markdown images

  // Long sentences (code rarely has sentences > 100 chars without symbols)
  /[.!?]\s+[A-Z][a-z]{10,}/,
];

export class CodeDetectionService {
  /**
   * Detect if content is code based on file extension and content analysis
   */
  detectCodeContent(filename: string, content: string): ICodeDetectionResult {
    const extension = `.${filename.split('.').pop()?.toLowerCase()}`;
    const reasons: string[] = [];
    let confidence = 0;
    let isCode = false;
    let detectedLanguage: string | undefined;

    // Step 1: Check file extension
    if (CODE_EXTENSIONS.includes(extension)) {
      isCode = true;
      confidence = 0.9;
      reasons.push(`File extension ${extension} indicates code`);
      detectedLanguage = this.getLanguageFromExtension(extension);
    } else if (AMBIGUOUS_EXTENSIONS.includes(extension)) {
      // Need content analysis for ambiguous extensions
      const contentAnalysis = this.analyzeContentPatterns(content);
      isCode = contentAnalysis.isCode;
      confidence = contentAnalysis.confidence;
      reasons.push(...contentAnalysis.reasons);

      if (isCode) {
        detectedLanguage = contentAnalysis.detectedLanguage;
      }
    } else {
      // Unknown extension, assume not code unless content strongly suggests otherwise
      const contentAnalysis = this.analyzeContentPatterns(content);
      if (contentAnalysis.confidence > 0.8) {
        isCode = contentAnalysis.isCode;
        confidence = contentAnalysis.confidence * 0.7; // Reduce confidence for unknown extensions
        reasons.push(...contentAnalysis.reasons);
        reasons.push('Unknown file extension, relying on content analysis');

        if (isCode) {
          detectedLanguage = contentAnalysis.detectedLanguage;
        }
      } else {
        isCode = false;
        confidence = 0.8;
        reasons.push('Unknown file extension and content analysis inconclusive');
      }
    }

    return {
      isCode,
      confidence,
      detectedLanguage,
      reasons,
    };
  }

  /**
   * Analyze content patterns to determine if it's code
   */
  private analyzeContentPatterns(content: string): {
    isCode: boolean;
    confidence: number;
    reasons: string[];
    detectedLanguage?: string;
  } {
    const reasons: string[] = [];
    let codeScore = 0;
    let docScore = 0;
    let detectedLanguage: string | undefined;

    // Count code pattern matches
    let codeMatches = 0;
    const languageHints: Record<string, number> = {};

    for (const pattern of CODE_PATTERNS) {
      const matches = content.match(pattern);
      if (matches) {
        codeMatches++;
        codeScore += 1;

        // Language-specific hints
        if (pattern.source.includes('def\\s+') && matches) {
          languageHints.python = (languageHints.python || 0) + 2;
        } else if (pattern.source.includes('function\\s+') && matches) {
          languageHints.javascript = (languageHints.javascript || 0) + 2;
        } else if (pattern.source.includes('public\\s+') && matches) {
          languageHints.java = (languageHints.java || 0) + 2;
        } else if (pattern.source.includes('SELECT\\s+') && matches) {
          languageHints.sql = (languageHints.sql || 0) + 2;
        } else if (pattern.source.includes('<\\w+') && matches) {
          languageHints.html = (languageHints.html || 0) + 1;
        }
      }
    }

    // Count documentation pattern matches
    let docMatches = 0;
    for (const pattern of DOCUMENTATION_PATTERNS) {
      const matches = content.match(pattern);
      if (matches) {
        docMatches++;
        docScore += 1;
      }
    }

    // Additional heuristics
    const lines = content.split('\n');
    const nonEmptyLines = lines.filter((line) => line.trim().length > 0);

    // Check for high symbol density (common in code)
    const symbolDensity = this.calculateSymbolDensity(content);
    if (symbolDensity > 0.15) {
      codeScore += 2;
      reasons.push('High symbol density suggests code');
    } else if (symbolDensity < 0.05) {
      docScore += 1;
      reasons.push('Low symbol density suggests documentation');
    }

    // Check for indentation patterns (common in code)
    const indentationScore = this.calculateIndentationScore(lines);
    if (indentationScore > 0.3) {
      codeScore += 1;
      reasons.push('Consistent indentation patterns suggest code');
    }

    // Check average line length (code tends to have shorter lines)
    const avgLineLength =
      nonEmptyLines.reduce((sum, line) => sum + line.length, 0) / nonEmptyLines.length;
    if (avgLineLength < 50 && nonEmptyLines.length > 5) {
      codeScore += 1;
      reasons.push('Short average line length suggests code');
    } else if (avgLineLength > 80) {
      docScore += 1;
      reasons.push('Long average line length suggests documentation');
    }

    // Determine detected language
    if (Object.keys(languageHints).length > 0) {
      const topLanguage = Object.entries(languageHints).reduce((a, b) => (a[1] > b[1] ? a : b));
      detectedLanguage = topLanguage[0];
    }

    // Calculate final confidence and decision
    const totalScore = codeScore + docScore;
    const codeConfidence = totalScore > 0 ? codeScore / totalScore : 0.5;

    const isCode = codeConfidence > 0.6;
    const confidence = Math.abs(codeConfidence - 0.5) * 2; // Convert to 0-1 scale

    if (codeMatches > 0) {
      reasons.push(`Found ${codeMatches} code pattern matches`);
    }
    if (docMatches > 0) {
      reasons.push(`Found ${docMatches} documentation pattern matches`);
    }

    return {
      isCode,
      confidence: Math.min(confidence, 0.95), // Cap confidence at 95%
      reasons,
      detectedLanguage,
    };
  }

  /**
   * Calculate symbol density (ratio of non-alphanumeric characters)
   */
  private calculateSymbolDensity(content: string): number {
    const totalChars = content.length;
    if (totalChars === 0) return 0;

    const symbols = content.match(/[^a-zA-Z0-9\s]/g) || [];
    return symbols.length / totalChars;
  }

  /**
   * Calculate indentation consistency score
   */
  private calculateIndentationScore(lines: string[]): number {
    const indentedLines = lines.filter((line) => line.match(/^\s+\S/));
    if (indentedLines.length < 2) return 0;

    const indentLevels = indentedLines.map((line) => {
      const match = line.match(/^(\s+)/);
      return match ? match[1].length : 0;
    });

    // Check for consistent indentation (multiples of 2 or 4)
    const consistentIndents = indentLevels.filter((level) => level % 2 === 0 || level % 4 === 0);
    return consistentIndents.length / indentLevels.length;
  }

  /**
   * Get programming language from file extension
   */
  private getLanguageFromExtension(extension: string): string {
    const languageMap: Record<string, string> = {
      '.py': 'python',
      '.js': 'javascript',
      '.ts': 'typescript',
      '.jsx': 'javascript',
      '.tsx': 'typescript',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.r': 'r',
      '.sql': 'sql',
      '.sh': 'shell',
      '.bat': 'batch',
      '.ps1': 'powershell',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.less': 'less',
    };

    return languageMap[extension] || 'unknown';
  }
}

// Lazy singleton instance
let codeDetectionServiceInstance: CodeDetectionService | null = null;

/**
 * Get or create code detection service instance (lazy initialization)
 */
export const getCodeDetectionService = (): CodeDetectionService => {
  if (!codeDetectionServiceInstance) {
    codeDetectionServiceInstance = new CodeDetectionService();
  }
  return codeDetectionServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const codeDetectionService = new Proxy({} as CodeDetectionService, {
  get(_target, prop) {
    const service = getCodeDetectionService();
    const value = service[prop as keyof CodeDetectionService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
