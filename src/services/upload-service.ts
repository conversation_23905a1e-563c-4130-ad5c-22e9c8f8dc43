/**
 * Upload Service
 *
 * Service for handling file uploads and text submissions with chunking and embedding generation.
 */

import {
  DEFAULT_CHUNKING_CONFIG,
  type IBaseResponse,
  type IDuplicateResponse,
  type IFileChunkMetadata,
  type ISuccessResponse,
  type ITextChunkMetadata,
  SUPPORTED_EXTENSIONS,
  type TContentType,
  type TEmbeddingProvider,
  type TTextSubmissionRequest,
  validateChunkMetadata,
} from '@types';
import { getEnv, getVDB } from '@utils';
import { extractText } from 'unpdf';
import { ChunkingService } from './chunking-service';
import { CodeDetectionService } from './code-detection-service';
import { EmbeddingService } from './embedding-service';
import { KVDocumentService } from './kv-document-service';

// Error classes
export interface IFileValidationDetails {
  field?: string;
  expected?: string;
  actual?: string;
  [key: string]: unknown;
}

export class FileValidationError extends Error {
  constructor(
    message: string,
    public details?: IFileValidationDetails
  ) {
    super(message);
    this.name = 'FileValidationError';
  }
}

export class DatabaseError extends Error {
  constructor(
    message: string,
    public operation?: string
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// Document chunk interface for upload processing
export interface DocumentChunk {
  id: string;
  content: string;
  metadata: IFileChunkMetadata | ITextChunkMetadata;
}

export class UploadService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private allowedTypes: string[] | null = null;
  private embeddingService: EmbeddingService;
  private chunkingService: ChunkingService;
  private codeDetectionService: CodeDetectionService;
  private kvDocumentService: KVDocumentService;

  constructor() {
    this.embeddingService = new EmbeddingService();
    this.chunkingService = new ChunkingService(DEFAULT_CHUNKING_CONFIG);
    this.codeDetectionService = new CodeDetectionService();
    this.kvDocumentService = new KVDocumentService();
  }

  private async getAllowedTypes(): Promise<string[]> {
    if (this.allowedTypes) {
      return this.allowedTypes;
    }

    try {
      const env = await getEnv();
      const envTypes = env.ALLOWED_FILE_EXTENSIONS;

      if (envTypes) {
        this.allowedTypes = envTypes.split(',').map((type) => type.trim());
      } else {
        this.allowedTypes = [...SUPPORTED_EXTENSIONS];
      }

      return this.allowedTypes;
    } catch (_error) {
      // Fallback to default types if there's an error accessing environment
      this.allowedTypes = [...SUPPORTED_EXTENSIONS];
      return this.allowedTypes;
    }
  }

  async processFileUpload(
    file: File,
    options: {
      category?: 'docs' | 'code';
      embedding_provider?: string;
      embedding_model?: string;
      collection?: string;
    } = {}
  ): Promise<ISuccessResponse | IDuplicateResponse | IBaseResponse> {
    const startTime = Date.now();

    try {
      // Validate file
      await this.validateFile(file);

      // Read file content based on file type
      const content = await this.extractFileContent(file);

      // Auto-detect code content if category not explicitly provided
      const finalOptions = await this.enhanceOptionsWithCodeDetection(file, content, options);

      // Generate content hash for duplicate detection
      const contentHash = await this.generateContentHash(content);

      // Check for duplicates
      const duplicateCheck = await this.checkForDuplicates(contentHash);
      if (duplicateCheck) {
        return duplicateCheck;
      }

      // Create chunks from content
      const chunks = await this.createChunks(content, file, contentHash, finalOptions);

      // Generate embeddings for chunks
      const embeddingResults = await this.generateEmbeddings(chunks, finalOptions);

      // Insert into vector database
      await this.insertIntoVectorDatabase(embeddingResults);

      const processingTime = Date.now() - startTime;

      // Get the embedding config from the service that was actually used
      const embeddingConfig =
        embeddingResults.length > 0
          ? {
              provider: embeddingResults[0].chunk.metadata.embedding_provider,
              model: embeddingResults[0].chunk.metadata.embedding_model,
              dimensions: embeddingResults[0].chunk.metadata.embedding_dimensions,
            }
          : await this.embeddingService.getConfig();

      return {
        status: 'success',
        timestamp: new Date().toISOString(),
        message: `Successfully processed and stored ${chunks.length} chunks from ${file.name}`,
        data: {
          file_name: file.name,
          file_size: file.size,
          content_hash: contentHash,
          category: finalOptions.category || 'docs',
          chunks_created: chunks.length,
          embedding_info: {
            provider: embeddingConfig.provider,
            model: embeddingConfig.model,
            dimensions: embeddingConfig.dimensions,
          },
          processing_time_ms: processingTime,
          upload_timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(
        `Upload processing failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  async processTextSubmission(
    request: TTextSubmissionRequest
  ): Promise<ISuccessResponse | IDuplicateResponse | IBaseResponse> {
    const startTime = Date.now();

    try {
      const { text, title, category, collection, embedding_provider, embedding_model } = request;

      // Auto-detect code content if category not explicitly provided
      const finalCategory = await this.enhanceTextCategoryWithCodeDetection(title, text, category);

      // Generate content hash for duplicate detection
      const contentHash = await this.generateContentHash(text);

      // Check for duplicates
      const duplicateCheck = await this.checkForDuplicates(contentHash);
      if (duplicateCheck) {
        return duplicateCheck;
      }

      // Create chunks from text content
      const chunks = await this.createTextChunks(text, title, contentHash, {
        category: finalCategory,
        collection,
      });

      // Generate embeddings for chunks
      const embeddingResults = await this.generateEmbeddings(chunks, {
        category: finalCategory,
        embedding_provider,
        embedding_model,
        collection,
      });

      // Insert into vector database
      await this.insertIntoVectorDatabase(embeddingResults);

      const processingTime = Date.now() - startTime;

      // Get the embedding config from the service that was actually used
      const embeddingConfig =
        embeddingResults.length > 0
          ? {
              provider: embeddingResults[0].chunk.metadata.embedding_provider,
              model: embeddingResults[0].chunk.metadata.embedding_model,
              dimensions: embeddingResults[0].chunk.metadata.embedding_dimensions,
            }
          : await this.embeddingService.getConfig();

      return {
        status: 'success',
        timestamp: new Date().toISOString(),
        message: `Successfully processed and stored ${chunks.length} chunks from text submission "${title}"`,
        data: {
          file_name: title,
          file_size: text.length,
          content_hash: contentHash,
          category: finalCategory,
          chunks_created: chunks.length,
          embedding_info: {
            provider: embeddingConfig.provider,
            model: embeddingConfig.model,
            dimensions: embeddingConfig.dimensions,
          },
          processing_time_ms: processingTime,
          upload_timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(
        `Text submission processing failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  public async validateFile(file: File): Promise<void> {
    // Check for empty files
    if (file.size === 0) {
      throw new FileValidationError('File cannot be empty', { file_size: file.size });
    }

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new FileValidationError(
        `File size exceeds maximum allowed size of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`,
        { file_size: file.size, max_size: this.MAX_FILE_SIZE }
      );
    }

    // Check file type
    const allowedTypes = await this.getAllowedTypes();
    const extension = this.getFileExtension(file.name);

    if (!allowedTypes.includes(extension)) {
      throw new FileValidationError(`File type ${extension} is not supported`, {
        file_type: extension,
        allowed_types: allowedTypes,
      });
    }
  }

  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex).toLowerCase() : '';
  }

  private async extractFileContent(file: File): Promise<string> {
    const extension = this.getFileExtension(file.name);

    switch (extension) {
      case '.txt':
      case '.md':
      case '.json':
      case '.py':
      case '.js':
      case '.ts':
      case '.jsx':
      case '.tsx':
      case '.java':
      case '.cpp':
      case '.c':
      case '.h':
      case '.cs':
      case '.php':
      case '.rb':
      case '.go':
      case '.rs':
      case '.swift':
      case '.kt':
      case '.scala':
      case '.r':
      case '.sql':
      case '.sh':
      case '.bat':
      case '.ps1':
      case '.yaml':
      case '.yml':
      case '.xml':
      case '.html':
      case '.css':
      case '.scss':
      case '.less':
        return await file.text();

      case '.pdf':
        // Extract text content from PDF using unpdf library
        try {
          const arrayBuffer = await file.arrayBuffer();
          const { text } = await extractText(arrayBuffer);

          // Join text array into a single string
          const extractedText = Array.isArray(text) ? text.join('\n') : text;

          if (!extractedText || extractedText.trim().length === 0) {
            throw new Error('No text content found in PDF');
          }

          return extractedText;
        } catch (error) {
          throw new FileValidationError(
            `Failed to extract content from PDF file: ${error instanceof Error ? error.message : String(error)}`,
            { file_type: 'pdf', file_name: file.name }
          );
        }

      case '.doc':
      case '.docx':
        // For Word documents, we'll extract text content
        // Note: This is a simplified implementation
        // In production, you'd want to use a proper document parsing library
        try {
          const content = await file.text();
          return content || `[Document Content from ${file.name}]`;
        } catch (error) {
          throw new FileValidationError(
            `Failed to extract content from document file: ${error instanceof Error ? error.message : String(error)}`,
            { file_type: extension, file_name: file.name }
          );
        }

      default:
        throw new FileValidationError(`Unsupported file type: ${extension}`, {
          file_type: extension,
          file_name: file.name,
        });
    }
  }

  private async enhanceOptionsWithCodeDetection(
    file: File,
    content: string,
    options: {
      category?: 'docs' | 'code';
      embedding_provider?: string;
      embedding_model?: string;
      collection?: string;
    }
  ) {
    // If category is explicitly provided, use it
    if (options.category) {
      return options;
    }

    // Auto-detect code content
    const detection = this.codeDetectionService.detectCodeContent(file.name, content);

    // Use code category if detection confidence is high enough
    const category: 'docs' | 'code' =
      detection.isCode && detection.confidence > 0.7 ? 'code' : 'docs';

    return {
      category,
      embedding_provider: options.embedding_provider,
      embedding_model: options.embedding_model,
      collection: options.collection,
    };
  }

  private async enhanceTextCategoryWithCodeDetection(
    title: string,
    text: string,
    category?: 'docs' | 'code'
  ): Promise<'docs' | 'code'> {
    // If category is explicitly provided, use it
    if (category) {
      return category;
    }

    // Auto-detect code content using title as filename hint
    const detection = this.codeDetectionService.detectCodeContent(title, text);

    // Use code category if detection confidence is high enough
    return detection.isCode && detection.confidence > 0.7 ? 'code' : 'docs';
  }

  public async generateContentHash(content: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(content);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
  }

  private async checkForDuplicates(contentHash: string): Promise<IDuplicateResponse | null> {
    try {
      const vdb = await getVDB();

      // Query for existing content with the same hash
      const dummyEmbedding = Array(1024).fill(0);
      const results = await vdb.query(dummyEmbedding, {
        topK: 1,
        returnMetadata: true,
        returnValues: false,
        filter: { content_hash: contentHash },
      });

      if (results.matches.length > 0) {
        const existingMatch = results.matches[0];
        const metadata = existingMatch.metadata || {};

        return {
          status: 'duplicate',
          timestamp: new Date().toISOString(),
          message: 'Content with identical hash already exists in the database',
          existing_content: {
            content_hash: contentHash,
            upload_timestamp: String(metadata.upload_timestamp || 'unknown'),
            chunks_count: Number(metadata.total_chunks || 1),
          },
        };
      }

      return null;
    } catch (error) {
      // If duplicate check fails, log the error but don't block the upload
      console.warn('Duplicate check failed:', error);
      return null;
    }
  }

  public async createChunks(
    content: string,
    file: File,
    contentHash: string,
    options: { category?: 'docs' | 'code'; collection?: string } = {}
  ): Promise<DocumentChunk[]> {
    const uploadTimestamp = new Date().toISOString();
    const contentType = this.getContentTypeFromExtension(file.name);

    // Create base metadata for chunks using unified schema
    const baseMetadata: Omit<IFileChunkMetadata, 'chunk_index' | 'total_chunks'> = {
      // Base metadata fields
      title: file.name,
      category: options.category || 'docs',
      content_type: contentType,
      content_hash: contentHash,
      timestamp: uploadTimestamp, // Unified timestamp field
      collection: options.collection,
      original_content: content,
      embedding_provider: '', // Will be set during embedding generation
      embedding_model: '', // Will be set during embedding generation
      embedding_dimensions: 0, // Will be set during embedding generation
      // File-specific metadata
      file_name: file.name,
      file_size: file.size,
      file_type: file.type || undefined,
    };

    // Use chunking service to create chunks
    const idPrefix = `upload-${contentHash.substring(0, 8)}-chunk`;
    const textChunks = this.chunkingService.createChunks<IFileChunkMetadata>(
      content,
      baseMetadata,
      idPrefix
    );

    // Convert to DocumentChunk format and validate metadata
    const documentChunks = textChunks.map((chunk) => ({
      id: chunk.id,
      content: chunk.content,
      metadata: chunk.metadata,
    }));

    // Validate metadata consistency for all chunks
    for (const chunk of documentChunks) {
      const validation = validateChunkMetadata(chunk.metadata);
      if (!validation.isValid) {
        throw new FileValidationError(
          `Metadata validation failed for chunk ${chunk.id}: ${validation.errors.join(', ')}`,
          { validation_errors: validation.errors }
        );
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        console.warn(`Metadata warnings for chunk ${chunk.id}:`, validation.warnings);
      }
    }

    return documentChunks;
  }

  private async createTextChunks(
    content: string,
    title: string,
    contentHash: string,
    options: { category?: 'docs' | 'code'; collection?: string } = {}
  ): Promise<DocumentChunk[]> {
    const uploadTimestamp = new Date().toISOString();

    // Create base metadata for chunks using unified schema
    const baseMetadata: Omit<ITextChunkMetadata, 'chunk_index' | 'total_chunks'> = {
      // Base metadata fields
      title: title,
      category: options.category || 'docs',
      content_type: 'text', // Text submissions are always treated as text
      content_hash: contentHash,
      timestamp: uploadTimestamp, // Unified timestamp field
      collection: options.collection,
      original_content: content,
      embedding_provider: '', // Will be set during embedding generation
      embedding_model: '', // Will be set during embedding generation
      embedding_dimensions: 0, // Will be set during embedding generation
    };

    // Use chunking service to create chunks
    const idPrefix = `text-${contentHash.substring(0, 8)}-chunk`;
    const textChunks = this.chunkingService.createChunks<ITextChunkMetadata>(
      content,
      baseMetadata,
      idPrefix
    );

    // Convert to DocumentChunk format and validate metadata
    const documentChunks = textChunks.map((chunk) => ({
      id: chunk.id,
      content: chunk.content,
      metadata: chunk.metadata,
    }));

    // Validate metadata consistency for all chunks
    for (const chunk of documentChunks) {
      const validation = validateChunkMetadata(chunk.metadata);
      if (!validation.isValid) {
        throw new FileValidationError(
          `Metadata validation failed for chunk ${chunk.id}: ${validation.errors.join(', ')}`,
          { validation_errors: validation.errors }
        );
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        console.warn(`Metadata warnings for chunk ${chunk.id}:`, validation.warnings);
      }
    }

    return documentChunks;
  }

  private getContentTypeFromExtension(filename: string): TContentType {
    const extension = this.getFileExtension(filename);

    // Map file extensions to content types
    const extensionMap: Record<string, TContentType> = {
      '.txt': 'text',
      '.md': 'markdown',
      '.json': 'json',
      '.pdf': 'pdf',
      '.doc': 'doc',
      '.docx': 'docx',
      '.py': 'python',
      '.js': 'javascript',
      '.ts': 'typescript',
      '.jsx': 'javascript',
      '.tsx': 'typescript',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'cpp',
      '.h': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.r': 'r',
      '.sql': 'sql',
      '.sh': 'shell',
      '.bat': 'shell',
      '.ps1': 'shell',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'css',
      '.less': 'css',
    };

    return extensionMap[extension] || 'text';
  }

  private async generateEmbeddings(
    chunks: DocumentChunk[],
    options: {
      category?: 'docs' | 'code';
      embedding_provider?: string;
      embedding_model?: string;
      collection?: string;
    } = {}
  ): Promise<Array<{ chunk: DocumentChunk; embedding: number[] }>> {
    const results: Array<{ chunk: DocumentChunk; embedding: number[] }> = [];

    // Create embedding service with custom configuration if provided
    const embeddingService =
      options.embedding_provider || options.embedding_model
        ? EmbeddingService.withConfig({
            provider: options.embedding_provider as TEmbeddingProvider,
            model: options.embedding_model,
          })
        : this.embeddingService;

    for (const chunk of chunks) {
      try {
        const embeddingResult = await embeddingService.generateEmbedding(chunk.content);

        // Update chunk metadata with embedding information
        chunk.metadata.embedding_provider = embeddingResult.provider;
        chunk.metadata.embedding_model = embeddingResult.model;
        chunk.metadata.embedding_dimensions = embeddingResult.dimensions;

        results.push({
          chunk,
          embedding: embeddingResult.embedding,
        });
      } catch (error) {
        throw new Error(
          `Failed to generate embedding for chunk ${chunk.id}: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }

    return results;
  }

  private async insertIntoVectorDatabase(
    results: Array<{ chunk: DocumentChunk; embedding: number[] }>
  ): Promise<void> {
    try {
      const vdb = await getVDB();

      // Prepare vectors for insertion
      const vectors: VectorizeVector[] = results.map((result) => ({
        id: result.chunk.id,
        values: result.embedding,
        metadata: this.convertToVectorizeMetadata({
          ...result.chunk.metadata,
          content: result.chunk.content, // Store chunk content in metadata for search
        }),
      }));

      // Insert vectors into the database
      await vdb.upsert(vectors);

      // Store vector metadata in KV for individual vector operations
      const kvPromises = results.map(async (result) => {
        const kvMetadata = {
          id: result.chunk.id,
          collection: result.chunk.metadata.collection,
          category: result.chunk.metadata.category,
          content_hash: result.chunk.metadata.content_hash,
          file_path:
            'file_name' in result.chunk.metadata ? result.chunk.metadata.file_name : undefined,
          title: result.chunk.metadata.title,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        return this.kvDocumentService.storeDocumentMetadata(result.chunk.id, kvMetadata);
      });

      await Promise.all(kvPromises);
    } catch (error) {
      throw new DatabaseError(
        `Vector database insertion failed: ${error instanceof Error ? error.message : String(error)}`,
        'upsert'
      );
    }
  }

  /**
   * Convert chunk metadata to Vectorize-compatible format
   */
  private convertToVectorizeMetadata(
    metadata: IFileChunkMetadata | ITextChunkMetadata
  ): Record<string, VectorizeVectorMetadata> {
    const converted: Record<string, VectorizeVectorMetadata> = {};

    for (const [key, value] of Object.entries(metadata)) {
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        converted[key] = value;
      } else if (Array.isArray(value)) {
        // Convert arrays to string arrays if they contain strings
        if (value.every((item) => typeof item === 'string')) {
          converted[key] = value as string[];
        } else {
          converted[key] = value.map(String);
        }
      } else if (value === null || value === undefined) {
        converted[key] = String(value);
      } else {
        // Convert objects to JSON strings
        converted[key] = JSON.stringify(value);
      }
    }

    return converted;
  }
}

// Lazy singleton instance
let uploadServiceInstance: UploadService | null = null;

/**
 * Get or create upload service instance (lazy initialization)
 */
export const getUploadService = (): UploadService => {
  if (!uploadServiceInstance) {
    uploadServiceInstance = new UploadService();
  }
  return uploadServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const uploadService = new Proxy({} as UploadService, {
  get(_target, prop) {
    const service = getUploadService();
    const value = service[prop as keyof UploadService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
