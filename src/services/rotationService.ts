/**
 * API Key Rotation Service
 *
 * Handles automatic rotation scheduling, policies, seamless transitions,
 * and rotation history tracking for API keys.
 */

import type {
  TA<PERSON><PERSON><PERSON>,
  TApiKeyRotationHistory,
  TApiKeyRotationPolicy,
  TNewApiKeyRotationHistory,
  TNewApiKeyRotationPolicy,
  TNewScheduledApiKeyRotation,
} from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, desc, eq, isNull, sql } from 'drizzle-orm';
import type { IApiKeyEmailContext } from '@/types/email';
import { rotateApiKey } from './apiKeyService';
import { notificationService } from './notificationService';

// ============================================================================
// Rotation Policy Types
// ============================================================================

/**
 * Automatic rotation policy
 */
export interface IRotationPolicy {
  id: string;
  userId: string;
  teamId?: string;

  // Policy settings
  enabled: boolean;
  rotationIntervalDays: number; // e.g., 90 days
  rotateBeforeExpirationDays?: number; // e.g., 7 days before expiration

  // Overlap settings for seamless transition
  overlapPeriodHours: number; // e.g., 24 hours overlap

  // Notification settings
  notifyBeforeRotation: boolean;
  notificationDays: number; // e.g., 3 days before rotation

  // Filters - which keys to apply this policy to
  keyNamePattern?: string; // regex pattern for key names
  permissions?: string[]; // only keys with these permissions

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  lastAppliedAt?: Date;
}

/**
 * Scheduled rotation job
 */
export interface IScheduledRotation {
  id: string;
  keyId: string;
  userId: string;
  teamId?: string;

  // Scheduling
  scheduledAt: Date;
  rotationType: 'automatic' | 'policy' | 'expiration' | 'manual';
  reason: string;

  // Overlap settings
  overlapPeriodHours: number;

  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';

  // Results
  newKeyId?: string;
  error?: string;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

/**
 * Rotation history with enhanced details
 */
export interface IRotationHistoryDetails extends TApiKeyRotationHistory {
  // Additional context
  overlapPeriodHours?: number;
  policyId?: string;
  scheduledRotationId?: string;

  // Key details at rotation time
  oldKeyUsageCount: number;
  oldKeyLastUsed?: Date;

  // Notification details
  notificationsSent: string[];

  // User/team context
  user?: {
    email: string;
    name: string;
  };
  team?: {
    name: string;
    slug: string;
  };
}

// ============================================================================
// Rotation Service Class
// ============================================================================

export class RotationService {
  /**
   * Create a rotation policy
   */
  async createRotationPolicy(
    policy: Omit<IRotationPolicy, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<IRotationPolicy> {
    const db = await getDB();

    // Create the rotation policy record in the database
    const newPolicyData: TNewApiKeyRotationPolicy = {
      userId: policy.userId,
      teamId: policy.teamId || undefined,
      enabled: policy.enabled,
      rotationIntervalDays: policy.rotationIntervalDays,
      rotateBeforeExpirationDays: policy.rotateBeforeExpirationDays || undefined,
      overlapPeriodHours: policy.overlapPeriodHours,
      notifyBeforeRotation: policy.notifyBeforeRotation,
      notificationDays: policy.notificationDays,
      keyNamePattern: policy.keyNamePattern || undefined,
      permissions: policy.permissions ? JSON.stringify(policy.permissions) : undefined,
      lastAppliedAt: policy.lastAppliedAt || undefined,
    };

    const [insertedPolicy] = await db
      .insert(schema.apiKeyRotationPolicies)
      .values(newPolicyData)
      .returning();

    // Convert database record to IRotationPolicy interface
    const rotationPolicy: IRotationPolicy = {
      id: insertedPolicy.id,
      userId: insertedPolicy.userId,
      teamId: insertedPolicy.teamId || undefined,
      enabled: insertedPolicy.enabled,
      rotationIntervalDays: insertedPolicy.rotationIntervalDays,
      rotateBeforeExpirationDays: insertedPolicy.rotateBeforeExpirationDays || undefined,
      overlapPeriodHours: insertedPolicy.overlapPeriodHours,
      notifyBeforeRotation: insertedPolicy.notifyBeforeRotation,
      notificationDays: insertedPolicy.notificationDays,
      keyNamePattern: insertedPolicy.keyNamePattern || undefined,
      permissions: insertedPolicy.permissions ? JSON.parse(insertedPolicy.permissions) : undefined,
      createdAt: insertedPolicy.createdAt,
      updatedAt: insertedPolicy.updatedAt,
      lastAppliedAt: insertedPolicy.lastAppliedAt || undefined,
    };

    return rotationPolicy;
  }

  /**
   * Get rotation policies for a user/team
   */
  async getRotationPolicies(userId: string, teamId?: string): Promise<IRotationPolicy[]> {
    const db = await getDB();

    const policies = await db.query.apiKeyRotationPolicies.findMany({
      where: and(
        eq(schema.apiKeyRotationPolicies.userId, userId),
        teamId
          ? eq(schema.apiKeyRotationPolicies.teamId, teamId)
          : isNull(schema.apiKeyRotationPolicies.teamId)
      ),
      orderBy: [desc(schema.apiKeyRotationPolicies.createdAt)],
    });

    // Convert database records to IRotationPolicy interfaces
    return policies.map((policy) => ({
      id: policy.id,
      userId: policy.userId,
      teamId: policy.teamId || undefined,
      enabled: policy.enabled,
      rotationIntervalDays: policy.rotationIntervalDays,
      rotateBeforeExpirationDays: policy.rotateBeforeExpirationDays || undefined,
      overlapPeriodHours: policy.overlapPeriodHours,
      notifyBeforeRotation: policy.notifyBeforeRotation,
      notificationDays: policy.notificationDays,
      keyNamePattern: policy.keyNamePattern || undefined,
      permissions: policy.permissions ? JSON.parse(policy.permissions) : undefined,
      createdAt: policy.createdAt,
      updatedAt: policy.updatedAt,
      lastAppliedAt: policy.lastAppliedAt || undefined,
    }));
  }

  /**
   * Update a rotation policy
   */
  async updateRotationPolicy(
    policyId: string,
    updates: Partial<Omit<IRotationPolicy, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<IRotationPolicy | null> {
    const db = await getDB();

    const updateData: Partial<TNewApiKeyRotationPolicy> = {};

    if (updates.enabled !== undefined) updateData.enabled = updates.enabled;
    if (updates.rotationIntervalDays !== undefined)
      updateData.rotationIntervalDays = updates.rotationIntervalDays;
    if (updates.rotateBeforeExpirationDays !== undefined)
      updateData.rotateBeforeExpirationDays = updates.rotateBeforeExpirationDays;
    if (updates.overlapPeriodHours !== undefined)
      updateData.overlapPeriodHours = updates.overlapPeriodHours;
    if (updates.notifyBeforeRotation !== undefined)
      updateData.notifyBeforeRotation = updates.notifyBeforeRotation;
    if (updates.notificationDays !== undefined)
      updateData.notificationDays = updates.notificationDays;
    if (updates.keyNamePattern !== undefined) updateData.keyNamePattern = updates.keyNamePattern;
    if (updates.permissions !== undefined)
      updateData.permissions = updates.permissions
        ? JSON.stringify(updates.permissions)
        : undefined;
    if (updates.lastAppliedAt !== undefined) updateData.lastAppliedAt = updates.lastAppliedAt;

    const [updatedPolicy] = await db
      .update(schema.apiKeyRotationPolicies)
      .set(updateData)
      .where(eq(schema.apiKeyRotationPolicies.id, policyId))
      .returning();

    if (!updatedPolicy) {
      return null;
    }

    // Convert database record to IRotationPolicy interface
    return {
      id: updatedPolicy.id,
      userId: updatedPolicy.userId,
      teamId: updatedPolicy.teamId || undefined,
      enabled: updatedPolicy.enabled,
      rotationIntervalDays: updatedPolicy.rotationIntervalDays,
      rotateBeforeExpirationDays: updatedPolicy.rotateBeforeExpirationDays || undefined,
      overlapPeriodHours: updatedPolicy.overlapPeriodHours,
      notifyBeforeRotation: updatedPolicy.notifyBeforeRotation,
      notificationDays: updatedPolicy.notificationDays,
      keyNamePattern: updatedPolicy.keyNamePattern || undefined,
      permissions: updatedPolicy.permissions ? JSON.parse(updatedPolicy.permissions) : undefined,
      createdAt: updatedPolicy.createdAt,
      updatedAt: updatedPolicy.updatedAt,
      lastAppliedAt: updatedPolicy.lastAppliedAt || undefined,
    };
  }

  /**
   * Delete a rotation policy
   */
  async deleteRotationPolicy(policyId: string): Promise<boolean> {
    const db = await getDB();

    const result = await db
      .delete(schema.apiKeyRotationPolicies)
      .where(eq(schema.apiKeyRotationPolicies.id, policyId))
      .returning();

    return result.length > 0;
  }

  /**
   * Schedule automatic rotation for an API key
   */
  async scheduleRotation(
    keyId: string,
    scheduledAt: Date,
    options: {
      rotationType?: 'automatic' | 'policy' | 'expiration' | 'manual';
      reason?: string;
      overlapPeriodHours?: number;
      userId?: string;
      teamId?: string;
    } = {}
  ): Promise<IScheduledRotation> {
    const db = await getDB();

    // Get the API key to extract user/team info
    const apiKey = await db.query.apiKeys.findFirst({
      where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
    });

    if (!apiKey) {
      throw new Error('API key not found');
    }

    // Create the scheduled rotation record in the database
    const newScheduledRotation: TNewScheduledApiKeyRotation = {
      keyId,
      userId: options.userId || apiKey.userId,
      teamId: options.teamId || apiKey.teamId || undefined,
      scheduledAt,
      rotationType: options.rotationType || 'manual',
      reason: options.reason || 'Scheduled rotation',
      overlapPeriodHours: options.overlapPeriodHours || 24,
      status: 'pending',
    };

    const [insertedRotation] = await db
      .insert(schema.scheduledApiKeyRotations)
      .values(newScheduledRotation)
      .returning();

    // Convert to IScheduledRotation interface
    const scheduledRotation: IScheduledRotation = {
      id: insertedRotation.id,
      keyId: insertedRotation.keyId,
      userId: insertedRotation.userId,
      teamId: insertedRotation.teamId || undefined,
      scheduledAt: insertedRotation.scheduledAt,
      rotationType: insertedRotation.rotationType as
        | 'automatic'
        | 'policy'
        | 'expiration'
        | 'manual',
      reason: insertedRotation.reason || 'Scheduled rotation',
      overlapPeriodHours: insertedRotation.overlapPeriodHours,
      status: insertedRotation.status as
        | 'pending'
        | 'processing'
        | 'completed'
        | 'failed'
        | 'cancelled',
      createdAt: insertedRotation.createdAt,
      updatedAt: insertedRotation.updatedAt,
    };

    return scheduledRotation;
  }

  /**
   * Process pending scheduled rotations
   */
  async processPendingRotations(): Promise<{
    processed: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    try {
      const db = await getDB();
      const now = new Date();

      // Get pending rotations that are due for execution
      const pendingRotations = await db.query.scheduledApiKeyRotations.findMany({
        where: and(
          eq(schema.scheduledApiKeyRotations.status, 'pending'),
          sql`${schema.scheduledApiKeyRotations.scheduledAt} <= ${now}`
        ),
        with: {
          key: true,
          user: true,
          team: true,
        },
        orderBy: [schema.scheduledApiKeyRotations.scheduledAt],
        limit: 50, // Process in batches to avoid overwhelming the system
      });

      results.processed = pendingRotations.length;

      // Process each pending rotation
      for (const rotation of pendingRotations) {
        try {
          // Update status to processing
          await db
            .update(schema.scheduledApiKeyRotations)
            .set({
              status: 'processing',
              updatedAt: new Date(),
            })
            .where(eq(schema.scheduledApiKeyRotations.id, rotation.id));

          // Perform the actual rotation
          const rotationResult = await this.rotateApiKeyWithHistory(rotation.keyId, {
            rotationType: rotation.rotationType as 'automatic' | 'policy' | 'expiration' | 'manual',
            reason: rotation.reason || 'Scheduled rotation',
            overlapPeriodHours: rotation.overlapPeriodHours,
            sendNotification: true,
          });

          if (rotationResult.success) {
            // Mark as completed
            await db
              .update(schema.scheduledApiKeyRotations)
              .set({
                status: 'completed',
                executedAt: new Date(),
                updatedAt: new Date(),
              })
              .where(eq(schema.scheduledApiKeyRotations.id, rotation.id));

            results.successful++;
          } else {
            // Mark as failed
            await db
              .update(schema.scheduledApiKeyRotations)
              .set({
                status: 'failed',
                failureReason: rotationResult.error || 'Unknown error',
                updatedAt: new Date(),
              })
              .where(eq(schema.scheduledApiKeyRotations.id, rotation.id));

            results.failed++;
            results.errors.push(
              `Rotation ${rotation.id}: ${rotationResult.error || 'Unknown error'}`
            );
          }
        } catch (error) {
          // Mark as failed and record error
          try {
            await db
              .update(schema.scheduledApiKeyRotations)
              .set({
                status: 'failed',
                failureReason: error instanceof Error ? error.message : 'Processing error',
                updatedAt: new Date(),
              })
              .where(eq(schema.scheduledApiKeyRotations.id, rotation.id));
          } catch (updateError) {
            console.error('Failed to update rotation status:', updateError);
          }

          results.failed++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
          results.errors.push(`Rotation ${rotation.id}: ${errorMessage}`);
          console.error(`Failed to process rotation ${rotation.id}:`, error);
        }
      }

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database query error';
      results.errors.push(`Failed to fetch pending rotations: ${errorMessage}`);
      console.error('Failed to process pending rotations:', error);
      return results;
    }
  }

  /**
   * Rotate API key with enhanced tracking and notifications
   */
  async rotateApiKeyWithHistory(
    keyId: string,
    options: {
      rotationType?: 'automatic' | 'policy' | 'expiration' | 'manual';
      reason?: string;
      overlapPeriodHours?: number;
      rotatedBy?: string;
      ipAddress?: string;
      userAgent?: string;
      sendNotification?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    newApiKey?: TApiKey;
    newKey?: string;
    rotationHistory?: TApiKeyRotationHistory;
    error?: string;
  }> {
    const db = await getDB();

    try {
      // Get the original key with user/team info
      const originalKey = await db.query.apiKeys.findFirst({
        where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
        with: {
          user: {
            columns: {
              email: true,
              name: true,
            },
          },
          team: {
            columns: {
              name: true,
              slug: true,
            },
          },
        },
      });

      if (!originalKey) {
        return { success: false, error: 'API key not found' };
      }

      // Perform the rotation
      const rotationResult = await rotateApiKey(keyId);
      if (!rotationResult) {
        return { success: false, error: 'Failed to rotate API key' };
      }

      // Create detailed rotation history record
      const rotationHistoryData: TNewApiKeyRotationHistory = {
        oldKeyId: keyId,
        newKeyId: rotationResult.apiKey.id,
        userId: originalKey.userId,
        teamId: originalKey.teamId,
        rotationType: options.rotationType || 'manual',
        reason: options.reason || 'Manual rotation',
        oldKeyName: originalKey.name,
        oldKeyPrefix: originalKey.keyPrefix,
        newKeyPrefix: rotationResult.apiKey.keyPrefix,
        rotatedBy: options.rotatedBy || null,
        ipAddress: options.ipAddress || null,
        userAgent: options.userAgent || null,
      };

      const [rotationHistory] = await db
        .insert(schema.apiKeyRotationHistory)
        .values(rotationHistoryData)
        .returning();

      // Send notification if requested
      if (options.sendNotification !== false && originalKey.user?.email) {
        try {
          const context: IApiKeyEmailContext = {
            userName: originalKey.user.name || 'User',
            userEmail: originalKey.user.email,
            keyName: originalKey.name,
            keyPrefix: originalKey.keyPrefix,
            keyId: originalKey.id,
            teamName: originalKey.team?.name,
            teamId: originalKey.teamId || undefined,
            rotatedAt: new Date(),
            newKeyPrefix: rotationResult.apiKey.keyPrefix,
            manageUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${rotationResult.apiKey.id}`,
            rotateUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${rotationResult.apiKey.id}/rotate`,
            teamUrl: originalKey.teamId
              ? `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/teams/${originalKey.teamId}`
              : undefined,
          };

          await notificationService.queueNotification(
            'api_key_rotated',
            originalKey.userId,
            originalKey.user.email,
            context,
            {
              teamId: originalKey.teamId || undefined,
              priority: 'normal',
            }
          );
        } catch (notificationError) {
          console.error('Failed to queue rotation notification:', notificationError);
          // Don't fail the rotation due to notification errors
        }
      }

      return {
        success: true,
        newApiKey: rotationResult.apiKey,
        newKey: rotationResult.key,
        rotationHistory,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Get rotation history for an API key or user/team
   */
  async getRotationHistory(options: {
    keyId?: string;
    userId?: string;
    teamId?: string;
    limit?: number;
    offset?: number;
  }): Promise<IRotationHistoryDetails[]> {
    const db = await getDB();
    const { keyId, userId, teamId, limit = 50, offset = 0 } = options;

    const whereConditions = [];

    if (keyId) {
      whereConditions.push(
        sql`(${schema.apiKeyRotationHistory.oldKeyId} = ${keyId} OR ${schema.apiKeyRotationHistory.newKeyId} = ${keyId})`
      );
    }

    if (userId) {
      whereConditions.push(eq(schema.apiKeyRotationHistory.userId, userId));
    }

    if (teamId) {
      whereConditions.push(eq(schema.apiKeyRotationHistory.teamId, teamId));
    }

    const rotationHistory = await db.query.apiKeyRotationHistory.findMany({
      where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
      with: {
        user: {
          columns: {
            email: true,
            name: true,
          },
        },
        team: {
          columns: {
            name: true,
            slug: true,
          },
        },
        rotatedBy: {
          columns: {
            email: true,
            name: true,
          },
        },
      },
      orderBy: [desc(schema.apiKeyRotationHistory.rotatedAt)],
      limit,
      offset,
    });

    // Transform to detailed history objects with usage counts
    const historyWithUsage = await Promise.all(
      rotationHistory.map(async (history) => {
        // Get usage count from audit logs for the old key
        const usageCount = await db
          .select({ count: sql<number>`count(*)` })
          .from(schema.auditLogs)
          .where(
            and(
              eq(schema.auditLogs.resourceId, history.oldKeyId),
              eq(schema.auditLogs.resource, 'api_key'),
              eq(schema.auditLogs.action, 'api_key_used')
            )
          );

        // Get notification history (for now, return empty array as notifications aren't fully implemented)
        const notificationsSent: string[] = [];

        return {
          ...history,
          team: history.team || undefined, // Convert null to undefined
          oldKeyUsageCount: usageCount[0]?.count || 0,
          notificationsSent,
        };
      })
    );

    return historyWithUsage;
  }

  /**
   * Get scheduled rotations
   */
  async getScheduledRotations(
    options: {
      userId?: string;
      teamId?: string;
      status?: 'pending' | 'completed' | 'failed';
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<IScheduledRotation[]> {
    const db = await getDB();

    // Build where conditions
    const whereConditions = [];

    if (options.userId) {
      whereConditions.push(eq(schema.scheduledApiKeyRotations.userId, options.userId));
    }

    if (options.teamId) {
      whereConditions.push(eq(schema.scheduledApiKeyRotations.teamId, options.teamId));
    }

    if (options.status) {
      whereConditions.push(eq(schema.scheduledApiKeyRotations.status, options.status));
    }

    const scheduledRotations = await db.query.scheduledApiKeyRotations.findMany({
      where: whereConditions.length > 0 ? and(...whereConditions) : undefined,
      orderBy: [desc(schema.scheduledApiKeyRotations.scheduledAt)],
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    // Convert to IScheduledRotation interface
    return scheduledRotations.map((rotation) => ({
      id: rotation.id,
      keyId: rotation.keyId,
      userId: rotation.userId,
      teamId: rotation.teamId || undefined,
      scheduledAt: rotation.scheduledAt,
      rotationType: rotation.rotationType as 'automatic' | 'policy' | 'expiration' | 'manual',
      reason: rotation.reason || 'Scheduled rotation',
      overlapPeriodHours: rotation.overlapPeriodHours,
      status: rotation.status as 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled',
      createdAt: rotation.createdAt,
      updatedAt: rotation.updatedAt,
    }));
  }

  /**
   * Execute a scheduled rotation
   */
  async executeScheduledRotation(keyId: string): Promise<{
    success: boolean;
    newApiKey?: TApiKey;
    newKey?: string;
    error?: string;
  }> {
    try {
      const result = await this.rotateApiKeyWithHistory(keyId, {
        rotationType: 'automatic',
        reason: 'Scheduled rotation',
      });

      return {
        success: result.success,
        newApiKey: result.newApiKey,
        newKey: result.newKey,
        error: result.error,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Process multiple scheduled rotations in bulk
   */
  async processBulkRotations(keyIds: string[]): Promise<{
    processed: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      processed: keyIds.length,
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    for (const keyId of keyIds) {
      try {
        const result = await this.executeScheduledRotation(keyId);
        if (result.success) {
          results.successful++;
        } else {
          results.failed++;
          if (result.error) {
            results.errors.push(`${keyId}: ${result.error}`);
          }
        }
      } catch (error) {
        results.failed++;
        results.errors.push(
          `${keyId}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    return results;
  }

  /**
   * Apply rotation policies to eligible API keys
   */
  async applyRotationPolicies(): Promise<{
    policiesApplied: number;
    rotationsScheduled: number;
    errors: string[];
  }> {
    const results = {
      policiesApplied: 0,
      rotationsScheduled: 0,
      errors: [] as string[],
    };

    try {
      const db = await getDB();
      const now = new Date();

      // 1. Get all active rotation policies
      const activePolicies = await db.query.apiKeyRotationPolicies.findMany({
        where: eq(schema.apiKeyRotationPolicies.enabled, true),
        with: {
          user: true,
          team: true,
        },
      });

      // Process each policy
      for (const policy of activePolicies) {
        try {
          // 2. Find API keys that match this policy's criteria
          const eligibleKeys = await this.findKeysMatchingPolicy(policy);

          if (eligibleKeys.length === 0) {
            continue; // No keys match this policy
          }

          let rotationsScheduledForPolicy = 0;

          // 3. Check each key to see if it's due for rotation
          for (const apiKey of eligibleKeys) {
            try {
              const shouldRotate = await this.shouldKeyBeRotated(apiKey, policy, now);

              if (shouldRotate.shouldRotate) {
                // 4. Schedule rotation for this key
                const scheduledAt = shouldRotate.scheduledAt || now;

                // Check if rotation is already scheduled for this key
                const existingRotation = await db.query.scheduledApiKeyRotations.findFirst({
                  where: and(
                    eq(schema.scheduledApiKeyRotations.keyId, apiKey.id),
                    eq(schema.scheduledApiKeyRotations.status, 'pending')
                  ),
                });

                if (!existingRotation) {
                  await this.scheduleRotation(apiKey.id, scheduledAt, {
                    rotationType: 'policy',
                    reason: `Policy-based rotation: ${policy.id}`,
                    overlapPeriodHours: policy.overlapPeriodHours,
                    userId: apiKey.userId,
                    teamId: apiKey.teamId || undefined,
                  });

                  rotationsScheduledForPolicy++;

                  // 5. Send pre-rotation notification if configured
                  if (policy.notifyBeforeRotation && policy.notificationDays > 0) {
                    const notificationDate = new Date(scheduledAt);
                    notificationDate.setDate(notificationDate.getDate() - policy.notificationDays);

                    if (notificationDate <= now) {
                      // Send notification immediately if notification date has passed
                      await this.sendRotationNotification(apiKey, policy, scheduledAt);
                    }
                  }
                }
              }
            } catch (keyError) {
              const errorMessage =
                keyError instanceof Error ? keyError.message : 'Unknown key processing error';
              results.errors.push(
                `Failed to process key ${apiKey.id} for policy ${policy.id}: ${errorMessage}`
              );
              console.error(
                `Failed to process key ${apiKey.id} for policy ${policy.id}:`,
                keyError
              );
            }
          }

          // Update policy's lastAppliedAt timestamp
          await db
            .update(schema.apiKeyRotationPolicies)
            .set({
              lastAppliedAt: now,
              updatedAt: now,
            })
            .where(eq(schema.apiKeyRotationPolicies.id, policy.id));

          results.policiesApplied++;
          results.rotationsScheduled += rotationsScheduledForPolicy;
        } catch (policyError) {
          const errorMessage =
            policyError instanceof Error ? policyError.message : 'Unknown policy processing error';
          results.errors.push(`Failed to apply policy ${policy.id}: ${errorMessage}`);
          console.error(`Failed to apply policy ${policy.id}:`, policyError);
        }
      }

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database query error';
      results.errors.push(`Failed to apply rotation policies: ${errorMessage}`);
      console.error('Failed to apply rotation policies:', error);
      return results;
    }
  }

  /**
   * Find API keys that match a rotation policy's criteria
   */
  private async findKeysMatchingPolicy(policy: TApiKeyRotationPolicy): Promise<TApiKey[]> {
    const db = await getDB();

    // Build the base query conditions
    const conditions = [
      eq(schema.apiKeys.userId, policy.userId),
      isNull(schema.apiKeys.deletedAt), // Only active keys
    ];

    // Add team filter if policy is team-scoped
    if (policy.teamId) {
      conditions.push(eq(schema.apiKeys.teamId, policy.teamId));
    } else {
      conditions.push(isNull(schema.apiKeys.teamId));
    }

    // Get all matching keys
    const keys = await db.query.apiKeys.findMany({
      where: and(...conditions),
    });

    // Apply additional filters
    let filteredKeys = keys;

    // Filter by key name pattern if specified
    if (policy.keyNamePattern) {
      try {
        const regex = new RegExp(policy.keyNamePattern, 'i');
        filteredKeys = filteredKeys.filter((key) => regex.test(key.name));
      } catch (error) {
        console.error(
          `Invalid regex pattern in policy ${policy.id}: ${policy.keyNamePattern}`,
          error
        );
        // If regex is invalid, skip pattern filtering
      }
    }

    // Filter by permissions if specified
    if (policy.permissions) {
      try {
        const requiredPermissions = JSON.parse(policy.permissions) as string[];
        filteredKeys = filteredKeys.filter((key) => {
          if (!key.permissions) return false;

          try {
            const keyPermissions = JSON.parse(key.permissions) as string[];
            return requiredPermissions.some((permission) => keyPermissions.includes(permission));
          } catch (error) {
            console.error(`Failed to parse permissions for key ${key.id}:`, error);
            return false;
          }
        });
      } catch (error) {
        console.error(`Failed to parse policy permissions for policy ${policy.id}:`, error);
        // If permissions parsing fails, skip permission filtering
      }
    }

    return filteredKeys;
  }

  /**
   * Determine if a key should be rotated based on policy settings
   */
  private async shouldKeyBeRotated(
    apiKey: TApiKey,
    policy: TApiKeyRotationPolicy,
    now: Date
  ): Promise<{ shouldRotate: boolean; scheduledAt?: Date; reason?: string }> {
    // Calculate when the key should be rotated based on creation date + interval
    const rotationDue = new Date(apiKey.createdAt);
    rotationDue.setDate(rotationDue.getDate() + policy.rotationIntervalDays);

    // Check if rotation is due
    if (rotationDue <= now) {
      return {
        shouldRotate: true,
        scheduledAt: now,
        reason: `Rotation interval of ${policy.rotationIntervalDays} days exceeded`,
      };
    }

    // Check if key has an expiration date and should be rotated before expiration
    if (policy.rotateBeforeExpirationDays && apiKey.expiresAt) {
      const rotateBeforeDate = new Date(apiKey.expiresAt);
      rotateBeforeDate.setDate(rotateBeforeDate.getDate() - policy.rotateBeforeExpirationDays);

      if (rotateBeforeDate <= now) {
        return {
          shouldRotate: true,
          scheduledAt: now,
          reason: `Rotation scheduled ${policy.rotateBeforeExpirationDays} days before expiration`,
        };
      }
    }

    return { shouldRotate: false };
  }

  /**
   * Send rotation notification for a key
   */
  private async sendRotationNotification(
    apiKey: TApiKey,
    _policy: TApiKeyRotationPolicy,
    scheduledAt: Date
  ): Promise<void> {
    try {
      // Get user information for the notification
      const db = await getDB();
      const user = await db.query.users.findFirst({
        where: eq(schema.users.id, apiKey.userId),
      });

      if (!user?.email) {
        console.warn(`No email found for user ${apiKey.userId}, skipping rotation notification`);
        return;
      }

      // Get team information if applicable
      let teamName: string | undefined;
      if (apiKey.teamId) {
        const team = await db.query.teams.findFirst({
          where: eq(schema.teams.id, apiKey.teamId),
        });
        teamName = team?.name;
      }

      const emailContext: IApiKeyEmailContext = {
        userName: user.name || 'User',
        userEmail: user.email,
        keyName: apiKey.name,
        keyPrefix: apiKey.keyPrefix,
        keyId: apiKey.id,
        teamName,
        teamId: apiKey.teamId || undefined,
        expiresAt: apiKey.expiresAt || undefined,
        manageUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${apiKey.id}`,
        rotateUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${apiKey.id}/rotate`,
        teamUrl: apiKey.teamId
          ? `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/teams/${apiKey.teamId}`
          : undefined,
      };

      // Queue a rotation scheduled notification
      // Note: scheduledAt and policy parameters are available for future use in notification content
      await notificationService.queueNotification(
        'api_key_rotation_scheduled',
        apiKey.userId,
        user.email,
        emailContext,
        {
          teamId: apiKey.teamId || undefined,
          priority: 'normal',
          scheduledAt, // Use the scheduled date for the notification
        }
      );
    } catch (error) {
      console.error(`Failed to send rotation notification for key ${apiKey.id}:`, error);
      // Don't throw error - notification failure shouldn't stop rotation scheduling
    }
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let rotationServiceInstance: RotationService | null = null;

/**
 * Get or create rotation service instance (lazy initialization)
 */
export const getRotationService = (): RotationService => {
  if (!rotationServiceInstance) {
    rotationServiceInstance = new RotationService();
  }
  return rotationServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const rotationService = new Proxy({} as RotationService, {
  get(_target, prop) {
    const service = getRotationService();
    const value = service[prop as keyof RotationService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
