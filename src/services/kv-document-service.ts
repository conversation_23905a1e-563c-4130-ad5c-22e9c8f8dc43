/**
 * KV Document Service
 *
 * Service for managing document ID mappings and metadata in Cloudflare KV storage.
 * This service works around Vectorize limitations for individual document operations.
 */

import { getKV } from '@utils';

/**
 * Interface for document metadata stored in KV
 */
export interface IDocumentMetadata {
  id: string;
  collection?: string;
  category?: string;
  content_hash?: string;
  file_path?: string;
  title?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for KV key metadata
 */
export interface IKVKeyMetadata {
  collection?: string;
  category?: string;
  content_hash?: string;
  created_at?: string;
}

/**
 * Interface for document ID mapping stored in KV
 */
export interface IDocumentIdMapping {
  document_id: string;
  metadata: IDocumentMetadata;
}

/**
 * KV Document Service for managing document metadata and ID mappings
 */
export class KVDocumentService {
  private static readonly DOCUMENT_PREFIX = 'document:';
  private static readonly COLLECTION_PREFIX = 'collection:';
  private static readonly CATEGORY_PREFIX = 'category:';
  private static readonly HASH_PREFIX = 'hash:';

  /**
   * Stores document metadata in KV storage
   *
   * @param documentId - Unique document identifier
   * @param metadata - Document metadata to store
   * @returns Promise resolving when storage is complete
   *
   * @example
   * ```typescript
   * await kvDocumentService.storeDocumentMetadata('doc_123', {
   *   id: 'doc_123',
   *   collection: 'docs',
   *   category: 'code',
   *   content_hash: 'abc123',
   *   created_at: new Date().toISOString(),
   *   updated_at: new Date().toISOString()
   * });
   * ```
   */
  async storeDocumentMetadata(documentId: string, metadata: IDocumentMetadata): Promise<void> {
    const kv = await getKV();
    const key = `${KVDocumentService.DOCUMENT_PREFIX}${documentId}`;

    await kv.put(key, JSON.stringify(metadata), {
      metadata: {
        collection: metadata.collection || '',
        category: metadata.category || '',
        content_hash: metadata.content_hash || '',
        created_at: metadata.created_at,
      },
    });
  }

  /**
   * Retrieves document metadata from KV storage
   *
   * @param documentId - Document identifier to retrieve
   * @returns Promise resolving to document metadata or null if not found
   *
   * @example
   * ```typescript
   * const metadata = await kvDocumentService.getDocumentMetadata('doc_123');
   * if (metadata) {
   *   console.log(`Document collection: ${metadata.collection}`);
   * }
   * ```
   */
  async getDocumentMetadata(documentId: string): Promise<IDocumentMetadata | null> {
    const kv = await getKV();
    const key = `${KVDocumentService.DOCUMENT_PREFIX}${documentId}`;

    const result = await kv.getWithMetadata(key, 'json');
    return result.value as IDocumentMetadata | null;
  }

  /**
   * Retrieves multiple document metadata entries
   *
   * @param documentIds - Array of document IDs to retrieve (max 128)
   * @returns Promise resolving to Map of document metadata
   *
   * @example
   * ```typescript
   * const metadataMap = await kvDocumentService.getMultipleDocumentMetadata(['doc_1', 'doc_2']);
   * for (const [id, metadata] of metadataMap) {
   *   console.log(`Document ${id}: ${metadata?.collection}`);
   * }
   * ```
   */
  async getMultipleDocumentMetadata(
    documentIds: string[]
  ): Promise<Map<string, IDocumentMetadata | null>> {
    if (documentIds.length > 128) {
      throw new Error('Cannot retrieve more than 128 document metadata entries at once');
    }

    const kv = await getKV();
    const keys = documentIds.map((id) => `${KVDocumentService.DOCUMENT_PREFIX}${id}`);

    const results = await kv.getWithMetadata(keys, 'json');
    const metadataMap = new Map<string, IDocumentMetadata | null>();

    for (const [key, result] of results) {
      const documentId = key.replace(KVDocumentService.DOCUMENT_PREFIX, '');
      metadataMap.set(documentId, result.value as IDocumentMetadata | null);
    }

    return metadataMap;
  }

  /**
   * Deletes document metadata from KV storage
   *
   * @param documentId - Document identifier to delete
   * @returns Promise resolving when deletion is complete
   *
   * @example
   * ```typescript
   * await kvDocumentService.deleteDocumentMetadata('doc_123');
   * ```
   */
  async deleteDocumentMetadata(documentId: string): Promise<void> {
    const kv = await getKV();
    const key = `${KVDocumentService.DOCUMENT_PREFIX}${documentId}`;

    await kv.delete(key);
  }

  /**
   * Lists document IDs by collection
   *
   * @param collection - Collection name to filter by
   * @param limit - Maximum number of results (default: 100)
   * @returns Promise resolving to array of document IDs
   *
   * @example
   * ```typescript
   * const documentIds = await kvDocumentService.listDocumentsByCollection('docs', 50);
   * console.log(`Found ${documentIds.length} documents in docs collection`);
   * ```
   */
  async listDocumentsByCollection(collection: string, limit = 100): Promise<string[]> {
    const kv = await getKV();
    const prefix = `${KVDocumentService.DOCUMENT_PREFIX}`;

    const listResult = await kv.list({ prefix, limit });
    const documentIds: string[] = [];

    for (const key of listResult.keys) {
      const metadata = key.metadata as IKVKeyMetadata | undefined;
      if (metadata?.collection === collection) {
        const documentId = key.name.replace(prefix, '');
        documentIds.push(documentId);
      }
    }

    return documentIds;
  }

  /**
   * Lists document IDs by category
   *
   * @param category - Category to filter by
   * @param limit - Maximum number of results (default: 100)
   * @returns Promise resolving to array of document IDs
   *
   * @example
   * ```typescript
   * const documentIds = await kvDocumentService.listDocumentsByCategory('code', 50);
   * console.log(`Found ${documentIds.length} code documents`);
   * ```
   */
  async listDocumentsByCategory(category: string, limit = 100): Promise<string[]> {
    const kv = await getKV();
    const prefix = `${KVDocumentService.DOCUMENT_PREFIX}`;

    const listResult = await kv.list({ prefix, limit });
    const documentIds: string[] = [];

    for (const key of listResult.keys) {
      const metadata = key.metadata as IKVKeyMetadata | undefined;
      if (metadata?.category === category) {
        const documentId = key.name.replace(prefix, '');
        documentIds.push(documentId);
      }
    }

    return documentIds;
  }

  /**
   * Finds document ID by content hash
   *
   * @param contentHash - Content hash to search for
   * @returns Promise resolving to document ID or null if not found
   *
   * @example
   * ```typescript
   * const documentId = await kvDocumentService.findDocumentByContentHash('abc123');
   * if (documentId) {
   *   console.log(`Found duplicate content: ${documentId}`);
   * }
   * ```
   */
  async findDocumentByContentHash(contentHash: string): Promise<string | null> {
    const kv = await getKV();
    const prefix = `${KVDocumentService.DOCUMENT_PREFIX}`;

    const listResult = await kv.list({ prefix, limit: 1000 });

    for (const key of listResult.keys) {
      const metadata = key.metadata as IKVKeyMetadata | undefined;
      if (metadata?.content_hash === contentHash) {
        return key.name.replace(prefix, '');
      }
    }

    return null;
  }

  /**
   * Updates document metadata
   *
   * @param documentId - Document identifier to update
   * @param updates - Partial metadata updates
   * @returns Promise resolving when update is complete
   *
   * @example
   * ```typescript
   * await kvDocumentService.updateDocumentMetadata('doc_123', {
   *   collection: 'updated-docs',
   *   updated_at: new Date().toISOString()
   * });
   * ```
   */
  async updateDocumentMetadata(
    documentId: string,
    updates: Partial<IDocumentMetadata>
  ): Promise<void> {
    const existingMetadata = await this.getDocumentMetadata(documentId);
    if (!existingMetadata) {
      throw new Error(`Document metadata not found for ID: ${documentId}`);
    }

    const updatedMetadata: IDocumentMetadata = {
      ...existingMetadata,
      ...updates,
      updated_at: new Date().toISOString(),
    };

    await this.storeDocumentMetadata(documentId, updatedMetadata);
  }

  /**
   * Bulk deletes document metadata by filters
   *
   * @param filters - Filter criteria for bulk deletion
   * @returns Promise resolving to count of deleted documents
   *
   * @example
   * ```typescript
   * const deletedCount = await kvDocumentService.bulkDeleteDocumentMetadata({
   *   collection: 'old-docs'
   * });
   * console.log(`Deleted ${deletedCount} documents`);
   * ```
   */
  async bulkDeleteDocumentMetadata(filters: {
    collection?: string;
    category?: string;
    content_hash?: string;
  }): Promise<number> {
    const kv = await getKV();
    const prefix = `${KVDocumentService.DOCUMENT_PREFIX}`;

    const listResult = await kv.list({ prefix, limit: 1000 });
    const keysToDelete: string[] = [];

    for (const key of listResult.keys) {
      const metadata = key.metadata as IKVKeyMetadata | undefined;
      let shouldDelete = true;

      if (filters.collection && metadata?.collection !== filters.collection) {
        shouldDelete = false;
      }
      if (filters.category && metadata?.category !== filters.category) {
        shouldDelete = false;
      }
      if (filters.content_hash && metadata?.content_hash !== filters.content_hash) {
        shouldDelete = false;
      }

      if (shouldDelete) {
        keysToDelete.push(key.name);
      }
    }

    // Delete in batches of 128 (KV limit)
    const batchSize = 128;
    for (let i = 0; i < keysToDelete.length; i += batchSize) {
      const batch = keysToDelete.slice(i, i + batchSize);
      await Promise.all(batch.map((key) => kv.delete(key)));
    }

    return keysToDelete.length;
  }
}

// Lazy singleton instance
let kvDocumentServiceInstance: KVDocumentService | null = null;

/**
 * Get or create KV document service instance (lazy initialization)
 */
export const getKVDocumentService = (): KVDocumentService => {
  if (!kvDocumentServiceInstance) {
    kvDocumentServiceInstance = new KVDocumentService();
  }
  return kvDocumentServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const kvDocumentService = new Proxy({} as KVDocumentService, {
  get(_target, prop) {
    const service = getKVDocumentService();
    const value = service[prop as keyof KVDocumentService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
