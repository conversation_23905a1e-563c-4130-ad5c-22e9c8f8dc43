/**
 * PayPal Service
 *
 * PayPal payment provider integration for subscription management,
 * one-time payments, webhook handling, and billing operations.
 */

import { Client, Environment } from '@paypal/paypal-server-sdk';
import { getEnv } from '@utils';
import type {
  ICreatePaymentIntentParams,
  ICreateSubscriptionParams,
  IUpdateSubscriptionParams,
  IWebhookEvent,
} from '@/types/billing';
import { createAuditLog } from './auditService';
import { billingService } from './billingService';

// ============================================================================
// PayPal Service Class
// ============================================================================

export class PayPalService {
  private client: Client | null = null;

  /**
   * Initialize PayPal SDK
   */
  private async initializePayPal(): Promise<Client> {
    if (this.client) {
      return this.client;
    }

    const env = await getEnv();

    if (!env.PAYPAL_CLIENT_ID || !env.PAYPAL_CLIENT_SECRET) {
      throw new Error(
        'PAYPAL_CLIENT_ID and PAYPAL_CLIENT_SECRET environment variables are required'
      );
    }

    this.client = new Client({
      clientCredentialsAuthCredentials: {
        oAuthClientId: env.PAYPAL_CLIENT_ID,
        oAuthClientSecret: env.PAYPAL_CLIENT_SECRET,
      },
      environment:
        (env.PAYPAL_ENVIRONMENT as string) === 'production'
          ? Environment.Production
          : Environment.Sandbox,
    });

    return this.client;
  }

  /**
   * Create a subscription with PayPal
   */
  async createSubscription(params: ICreateSubscriptionParams): Promise<{
    localSubscription: {
      id: string;
      teamId: string;
      planId: string;
      status: string;
    };
    paypalSubscription: {
      id: string;
      status: string;
      plan_id: string;
      links?: Array<{ rel: string; href: string }>;
    };
  }> {
    await this.initializePayPal();

    try {
      // Get plan configuration
      const planConfig = billingService.getAvailablePlans().find((p) => p.id === params.planId);
      if (!planConfig) {
        throw new Error(`Plan ${params.planId} not found`);
      }

      // Get PayPal plan ID based on interval
      const paypalPlanId =
        params.interval === 'monthly'
          ? planConfig.pricing.monthly.paypalPlanId
          : planConfig.pricing.yearly.paypalPlanId;

      if (!paypalPlanId) {
        throw new Error(`PayPal plan ID not configured for ${params.planId} ${params.interval}`);
      }

      // Create subscription with PayPal
      const subscriptionRequest = {
        plan_id: paypalPlanId,
        subscriber: {
          name: {
            given_name: 'Team',
            surname: params.teamId,
          },
        },
        application_context: {
          brand_name: 'EZContext',
          locale: 'en-US',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'SUBSCRIBE_NOW',
          payment_method: {
            payer_selected: 'PAYPAL',
            payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED',
          },
          return_url: `${await this.getBaseUrl()}/billing/paypal/success`,
          cancel_url: `${await this.getBaseUrl()}/billing/paypal/cancel`,
        },
      };

      // PayPal subscriptions are typically created through checkout flows
      // For now, we'll create a placeholder subscription and handle the actual
      // PayPal subscription creation through webhooks
      const subscription = {
        id: `paypal_sub_${params.teamId}_${Date.now()}`,
        status: 'pending',
        plan_id: paypalPlanId,
        subscriber: subscriptionRequest.subscriber,
        application_context: subscriptionRequest.application_context,
      };

      // Create local subscription record
      const localSubscription = await billingService.createSubscription({
        ...params,
        provider: 'paypal',
      });

      // Log the subscription creation
      await createAuditLog({
        teamId: params.teamId,
        action: 'billing.subscription_create',
        resource: 'billing_subscription',
        resourceId: localSubscription.id,
        details: {
          provider: 'paypal',
          planId: params.planId,
          interval: params.interval,
          paypalSubscriptionId: subscription.id,
        },
      });

      return {
        localSubscription,
        paypalSubscription: subscription,
      };
    } catch (error) {
      console.error('PayPal subscription creation failed:', error);
      throw new Error(
        `Failed to create PayPal subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Update a subscription with PayPal
   */
  async updateSubscription(
    subscriptionId: string,
    params: IUpdateSubscriptionParams
  ): Promise<{
    id: string;
    teamId: string;
    planId: string;
    status: string;
  }> {
    await this.initializePayPal();

    try {
      // Get local subscription
      const localSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!localSubscription) {
        throw new Error('Subscription not found');
      }

      // PayPal doesn't support direct plan changes, need to cancel and create new
      if (params.planId) {
        console.log('PayPal plan changes require cancellation and recreation');
        // This would typically involve canceling the current subscription
        // and creating a new one with the new plan
      }

      // Update local subscription
      const updatedSubscription = await billingService.updateSubscription(subscriptionId, params);

      // Log the subscription update
      await createAuditLog({
        teamId: localSubscription.teamId,
        action: 'billing.subscription_update',
        resource: 'billing_subscription',
        resourceId: subscriptionId,
        details: {
          provider: 'paypal',
          changes: params,
        },
      });

      if (!updatedSubscription) {
        throw new Error('Failed to update subscription');
      }

      return {
        id: updatedSubscription.id,
        teamId: updatedSubscription.teamId,
        planId: updatedSubscription.planId,
        status: updatedSubscription.status,
      };
    } catch (error) {
      console.error('PayPal subscription update failed:', error);
      throw new Error(
        `Failed to update PayPal subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Cancel a subscription with PayPal
   */
  async cancelSubscription(subscriptionId: string, reason?: string): Promise<void> {
    await this.initializePayPal();

    try {
      // Get local subscription
      const localSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!localSubscription) {
        throw new Error('Subscription not found');
      }

      // Cancel with PayPal
      // Note: PayPal subscription cancellation would be handled through their API
      // For now, we'll just update the local subscription status
      console.log(
        `Canceling PayPal subscription ${subscriptionId} with reason: ${reason || 'User requested cancellation'}`
      );

      // Update local subscription
      await billingService.cancelSubscription(subscriptionId);

      // Log the subscription cancellation
      await createAuditLog({
        teamId: localSubscription.teamId,
        action: 'billing.subscription_cancel',
        resource: 'billing_subscription',
        resourceId: subscriptionId,
        details: {
          provider: 'paypal',
          reason: reason || 'User requested cancellation',
        },
      });

      // Subscription canceled successfully
    } catch (error) {
      console.error('PayPal subscription cancellation failed:', error);
      throw new Error(
        `Failed to cancel PayPal subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Create a one-time payment with PayPal
   */
  async createPayment(params: ICreatePaymentIntentParams): Promise<{
    id: string;
    status: string;
    amount: {
      currency_code: string;
      value: string;
    };
    links?: Array<{ rel: string; href: string }>;
  }> {
    await this.initializePayPal();

    try {
      const orderRequest = {
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: params.currency,
              value: (params.amount / 100).toFixed(2), // Convert cents to dollars
            },
            description: params.description,
            custom_id: params.teamId,
          },
        ],
        application_context: {
          brand_name: 'EZContext',
          locale: 'en-US',
          landing_page: 'BILLING',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'PAY_NOW',
          return_url: `${await this.getBaseUrl()}/billing/paypal/success`,
          cancel_url: `${await this.getBaseUrl()}/billing/paypal/cancel`,
        },
      };

      // PayPal orders would be created through their API
      // For now, we'll create a placeholder order
      const order = {
        id: `paypal_order_${params.teamId}_${Date.now()}`,
        status: 'pending',
        intent: 'CAPTURE',
        purchase_units: orderRequest.purchase_units,
        application_context: orderRequest.application_context,
      };

      // Log the payment creation
      await createAuditLog({
        teamId: params.teamId,
        action: 'billing.payment_success',
        resource: 'billing_transaction',
        resourceId: order.id,
        details: {
          provider: 'paypal',
          amount: params.amount,
          currency: params.currency,
          description: params.description,
        },
      });

      return {
        id: order.id,
        status: order.status,
        amount: {
          currency_code: params.currency,
          value: (params.amount / 100).toFixed(2),
        },
        links: order.application_context ? [] : undefined,
      };
    } catch (error) {
      console.error('PayPal payment creation failed:', error);
      throw new Error(
        `Failed to create PayPal payment: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Capture a PayPal payment
   */
  async capturePayment(orderId: string): Promise<{
    id: string;
    status: string;
    amount: {
      currency_code: string;
      value: string;
    };
    final_capture: boolean;
  }> {
    await this.initializePayPal();

    try {
      // PayPal payment capture would be handled through their API
      // For now, we'll return a placeholder capture result
      const capture = {
        id: `capture_${orderId}_${Date.now()}`,
        status: 'COMPLETED',
        amount: {
          currency_code: 'USD',
          value: '0.00',
        },
        final_capture: true,
      };

      return capture;
    } catch (error) {
      console.error('PayPal payment capture failed:', error);
      throw new Error(
        `Failed to capture PayPal payment: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Handle PayPal webhook events
   */
  async handleWebhook(event: IWebhookEvent): Promise<void> {
    try {
      console.log(`Processing PayPal webhook: ${event.type}`);

      switch (event.type) {
        case 'BILLING.SUBSCRIPTION.CREATED':
          await this.handleSubscriptionCreated(event);
          break;
        case 'BILLING.SUBSCRIPTION.UPDATED':
          await this.handleSubscriptionUpdated(event);
          break;
        case 'BILLING.SUBSCRIPTION.CANCELLED':
          await this.handleSubscriptionCanceled(event);
          break;
        case 'PAYMENT.SALE.COMPLETED':
          await this.handlePaymentCompleted(event);
          break;
        case 'PAYMENT.SALE.DENIED':
          await this.handlePaymentFailed(event);
          break;
        default:
          console.log(`Unhandled PayPal webhook event: ${event.type}`);
      }
    } catch (error) {
      console.error('PayPal webhook handling failed:', error);
      throw error;
    }
  }

  /**
   * Verify PayPal webhook signature
   */
  async verifyWebhookSignature(payload: string, headers: Record<string, string>): Promise<boolean> {
    const env = await getEnv();

    if (!env.PAYPAL_WEBHOOK_ID) {
      console.warn('PAYPAL_WEBHOOK_ID not configured, skipping signature verification');
      return true; // Allow in development
    }

    try {
      await this.initializePayPal();

      const verifyRequest = {
        auth_algo: headers['PAYPAL-AUTH-ALGO'] || '',
        cert_id: headers['PAYPAL-CERT-ID'] || '',
        transmission_id: headers['PAYPAL-TRANSMISSION-ID'] || '',
        transmission_sig: headers['PAYPAL-TRANSMISSION-SIG'] || '',
        transmission_time: headers['PAYPAL-TRANSMISSION-TIME'] || '',
        webhook_id: env.PAYPAL_WEBHOOK_ID,
        webhook_event: JSON.parse(payload),
      };

      // PayPal webhook verification would be handled through their API
      // For now, we'll return true for development
      console.log('PayPal webhook verification request:', verifyRequest);
      return true;
    } catch (error) {
      console.error('PayPal webhook signature verification failed:', error);
      return false;
    }
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private async getBaseUrl(): Promise<string> {
    const env = await getEnv();
    return env.BASE_URL || 'http://localhost:8787';
  }

  // ============================================================================
  // Private Webhook Handlers
  // ============================================================================

  private async handleSubscriptionCreated(event: IWebhookEvent): Promise<void> {
    console.log('Handling PayPal subscription created:', event.data);
  }

  private async handleSubscriptionUpdated(event: IWebhookEvent): Promise<void> {
    console.log('Handling PayPal subscription updated:', event.data);
  }

  private async handleSubscriptionCanceled(event: IWebhookEvent): Promise<void> {
    console.log('Handling PayPal subscription canceled:', event.data);
  }

  private async handlePaymentCompleted(event: IWebhookEvent): Promise<void> {
    console.log('Handling PayPal payment completed:', event.data);
  }

  private async handlePaymentFailed(event: IWebhookEvent): Promise<void> {
    console.log('Handling PayPal payment failed:', event.data);
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let paypalServiceInstance: PayPalService | null = null;

/**
 * Get or create PayPal service instance (lazy initialization)
 */
export const getPayPalService = (): PayPalService => {
  if (!paypalServiceInstance) {
    paypalServiceInstance = new PayPalService();
  }
  return paypalServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const paypalService = new Proxy({} as PayPalService, {
  get(_target, prop) {
    const service = getPayPalService();
    const value = service[prop as keyof PayPalService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
