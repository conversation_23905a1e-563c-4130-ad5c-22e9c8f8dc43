/**
 * Document Service
 *
 * Service for managing document operations with Cloudflare Vectorize.
 */

import type {
  IBulkDeleteResponse,
  IDocumentItem,
  IDocumentListResponse,
  IDocumentResponse,
  IMetadata,
  IPaginationInfo,
} from '@types';
import { getVDB } from '@utils';
import { KVDocumentService } from './kv-document-service';

export class DocumentService {
  private kvDocumentService: KVDocumentService;

  constructor() {
    this.kvDocumentService = new KVDocumentService();
  }

  /**
   * Lists documents with pagination and optional filtering
   *
   * @param params - Pagination and filter parameters
   * @param params.page - Page number (1-based)
   * @param params.limit - Number of documents per page
   * @param params.collection - Optional collection filter
   * @param params.category - Optional category filter (docs/code)
   * @param params.userID - Optional user ID filter
   * @param params.projectID - Optional project ID filter
   * @param params.tenantID - Optional tenant ID filter
   * @returns Promise resolving to paginated document list with metadata
   * @throws Error if document listing fails
   *
   * @example
   * ```typescript
   * const documents = await documentService.listDocuments({
   *   page: 1,
   *   limit: 10,
   *   collection: 'react',
   *   category: 'docs'
   * });
   * ```
   */
  async listDocuments(params: {
    page: number;
    limit: number;
    collection?: string;
    category?: string;
    userID?: string;
    projectID?: string;
    tenantID?: string;
  }): Promise<IDocumentListResponse> {
    try {
      // Use KV-based metadata-only listing for better performance
      const documentIds = await this.listDocumentIdsWithFilters(params);

      // Implement true pagination at the data level
      const startIndex = (params.page - 1) * params.limit;
      const endIndex = startIndex + params.limit;
      const paginatedIds = documentIds.slice(startIndex, endIndex);

      // Get metadata for paginated results
      const documents: IDocumentItem[] = [];
      if (paginatedIds.length > 0) {
        const metadataMap = await this.kvDocumentService.getMultipleDocumentMetadata(paginatedIds);

        for (const documentId of paginatedIds) {
          const kvMetadata = metadataMap.get(documentId);
          if (kvMetadata) {
            documents.push({
              id: documentId,
              metadata: this.convertKVMetadataToIMetadata(kvMetadata),
              score: 1.0, // Default score for listing operations
            });
          }
        }
      }

      const totalCount = documentIds.length;
      const totalPages = Math.ceil(totalCount / params.limit);

      const pagination: IPaginationInfo = {
        current_page: params.page,
        total_pages: totalPages,
        total_count: totalCount,
        has_next: params.page < totalPages,
        has_previous: params.page > 1,
        limit: params.limit,
      };

      return {
        status: 'success',
        data: {
          documents,
          pagination,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to list documents: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Retrieves a specific document by its ID
   *
   * @param id - The unique identifier of the document to retrieve
   * @returns Promise resolving to document data with metadata
   * @throws Error if document retrieval fails
   *
   * @example
   * ```typescript
   * const document = await documentService.getDocument('doc-123');
   * if (document.status === 'success') {
   *   console.log('Document metadata:', document.data?.metadata);
   * }
   * ```
   */
  async getDocument(id: string): Promise<IDocumentResponse> {
    try {
      const vdb = await getVDB();

      // First check if document exists in KV storage for enhanced metadata
      const kvMetadata = await this.kvDocumentService.getDocumentMetadata(id);

      // Use the correct getByIds method to retrieve document by ID
      const results = await vdb.getByIds([id]);

      if (results.length === 0) {
        // If document not found in Vectorize but exists in KV, clean up KV
        if (kvMetadata) {
          await this.kvDocumentService.deleteDocumentMetadata(id);
        }

        return {
          status: 'error',
          timestamp: new Date().toISOString(),
        } as IDocumentResponse;
      }

      const document = results[0];

      // Merge Vectorize metadata with KV metadata for enhanced information
      const combinedMetadata = {
        ...this.convertVectorizeMetadata(document.metadata || {}),
        ...(kvMetadata
          ? {
              created_at: kvMetadata.created_at,
              updated_at: kvMetadata.updated_at,
              content_hash: kvMetadata.content_hash,
            }
          : {}),
      };

      return {
        status: 'success',
        data: {
          id: document.id,
          metadata: combinedMetadata,
          // Note: getByIds doesn't return a score since it's not a similarity search
          score: undefined,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to get document: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Updates a document's metadata while preserving its embedding values
   *
   * @param id - The unique identifier of the document to update
   * @param updates - Object containing metadata updates
   * @param updates.metadata - New metadata to merge with existing metadata
   * @returns Promise resolving to updated document response
   * @throws Error if document update fails or document not found
   *
   * @example
   * ```typescript
   * const result = await documentService.updateDocument('doc-123', {
   *   metadata: {
   *     category: 'updated-docs',
   *     last_modified: new Date().toISOString()
   *   }
   * });
   * ```
   */
  async updateDocument(id: string, updates: { metadata?: IMetadata }): Promise<IDocumentResponse> {
    try {
      const vdb = await getVDB();

      // First, get the existing document with its values
      const results = await vdb.getByIds([id]);
      if (results.length === 0) {
        return {
          status: 'error',
          timestamp: new Date().toISOString(),
        } as IDocumentResponse;
      }

      const existingDocument = results[0];

      // Merge metadata
      const updatedMetadata = {
        ...this.convertVectorizeMetadata(existingDocument.metadata || {}),
        ...updates.metadata,
      };

      // Use upsert with the original document values to preserve embeddings
      await vdb.upsert([
        {
          id: id,
          values: existingDocument.values,
          metadata: this.convertToVectorizeMetadata(updatedMetadata),
        },
      ]);

      // Update KV metadata if it exists
      try {
        const kvMetadata = await this.kvDocumentService.getDocumentMetadata(id);
        if (kvMetadata) {
          await this.kvDocumentService.updateDocumentMetadata(id, {
            collection:
              typeof updatedMetadata.collection === 'string'
                ? updatedMetadata.collection
                : undefined,
            category:
              typeof updatedMetadata.category === 'string' ? updatedMetadata.category : undefined,
            title: typeof updatedMetadata.title === 'string' ? updatedMetadata.title : undefined,
          });
        }
      } catch (kvError) {
        // Log KV update error but don't fail the main operation
        console.warn(`Failed to update KV metadata for document ${id}:`, kvError);
      }

      return {
        status: 'success',
        data: {
          id: id,
          metadata: updatedMetadata,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to update document: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Deletes a document from the database
   *
   * @param id - The unique identifier of the document to delete
   * @returns Promise resolving to deletion confirmation
   * @throws Error if document deletion fails or document not found
   *
   * @example
   * ```typescript
   * const result = await documentService.deleteDocument('doc-123');
   * if (result.status === 'success') {
   *   console.log('Document deleted successfully');
   * }
   * ```
   */
  async deleteDocument(id: string): Promise<IDocumentResponse> {
    try {
      const vdb = await getVDB();

      // Check if document exists first using getByIds
      const results = await vdb.getByIds([id]);
      if (results.length === 0) {
        return {
          status: 'error',
          timestamp: new Date().toISOString(),
        } as IDocumentResponse;
      }

      // Delete the document from Vectorize
      await vdb.deleteByIds([id]);

      // Clean up KV metadata
      try {
        await this.kvDocumentService.deleteDocumentMetadata(id);
      } catch (kvError) {
        // Log KV cleanup error but don't fail the main operation
        console.warn(`Failed to delete KV metadata for document ${id}:`, kvError);
      }

      return {
        status: 'success',
        data: {
          id: id,
          metadata: {},
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to delete document: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Deletes multiple documents based on filter criteria
   *
   * @param filters - Filter criteria for bulk deletion
   * @param filters.collection - Optional collection filter
   * @param filters.category - Optional category filter (docs/code)
   * @param filters.content_hash - Optional content hash filter
   * @returns Promise resolving to bulk deletion results with count
   * @throws Error if bulk deletion fails or no filters provided
   *
   * @example
   * ```typescript
   * const result = await documentService.bulkDelete({
   *   collection: 'old-docs',
   *   category: 'docs'
   * });
   * console.log(`Deleted ${result.data.deleted_count} documents`);
   * ```
   */
  async bulkDelete(filters: {
    collection?: string;
    category?: string;
    content_hash?: string;
  }): Promise<IBulkDeleteResponse> {
    try {
      const vdb = await getVDB();

      // Build filter object
      const filter: VectorizeVectorMetadataFilter = {};
      if (filters.collection) filter.collection = filters.collection;
      if (filters.category) filter.category = filters.category;
      if (filters.content_hash) filter.content_hash = filters.content_hash;

      if (Object.keys(filter).length === 0) {
        // Return success with 0 deleted items when no filters provided
        return {
          status: 'success',
          message: 'No filters provided - no documents deleted',
          data: {
            deleted_count: 0,
            filters_applied: {},
          },
          timestamp: new Date().toISOString(),
        };
      }

      // Use KV-based filtering instead of dummy embedding queries
      const documentIds = await this.listDocumentIdsWithFilters({
        collection: filter.collection as string | undefined,
        category: filter.category as string | undefined,
        userID: filter.userID as string | undefined,
        projectID: filter.projectID as string | undefined,
        tenantID: filter.tenantID as string | undefined,
      });

      if (documentIds.length === 0) {
        return {
          status: 'success',
          message: 'No documents found matching the specified filters',
          data: {
            deleted_count: 0,
            filters_applied: this.convertFilterToMetadata(filter),
          },
          timestamp: new Date().toISOString(),
        };
      }

      // Delete the matching documents
      await vdb.deleteByIds(documentIds);

      // Clean up KV metadata for deleted documents
      try {
        const kvDeletePromises = documentIds.map((id) =>
          this.kvDocumentService.deleteDocumentMetadata(id)
        );
        await Promise.all(kvDeletePromises);
      } catch (kvError) {
        // Log KV cleanup error but don't fail the main operation
        console.warn(`Failed to delete KV metadata for some documents:`, kvError);
      }

      return {
        status: 'success',
        message: `Successfully deleted ${documentIds.length} documents`,
        data: {
          deleted_count: documentIds.length,
          filters_applied: this.convertFilterToMetadata(filter),
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to bulk delete documents: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Convert Vectorize metadata to IMetadata format
   */
  private convertVectorizeMetadata(metadata: Record<string, VectorizeVectorMetadata>): IMetadata {
    const converted: IMetadata = {};

    for (const [key, value] of Object.entries(metadata)) {
      if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean' ||
        value === null
      ) {
        converted[key] = value;
      } else if (Array.isArray(value)) {
        // Convert arrays to comma-separated strings
        converted[key] = value.join(', ');
      } else if (typeof value === 'object' && value !== null) {
        // Convert objects to JSON strings
        converted[key] = JSON.stringify(value);
      } else {
        converted[key] = String(value);
      }
    }

    return converted;
  }

  /**
   * Convert filter to IMetadata format
   */
  private convertFilterToMetadata(filter: VectorizeVectorMetadataFilter): IMetadata {
    const converted: IMetadata = {};

    for (const [key, value] of Object.entries(filter)) {
      if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean' ||
        value === null
      ) {
        converted[key] = value;
      } else if (typeof value === 'object' && value !== null) {
        // Convert filter objects to JSON strings
        converted[key] = JSON.stringify(value);
      } else {
        converted[key] = String(value);
      }
    }

    return converted;
  }

  /**
   * Convert IMetadata to Vectorize metadata format
   */
  private convertToVectorizeMetadata(metadata: IMetadata): Record<string, VectorizeVectorMetadata> {
    const converted: Record<string, VectorizeVectorMetadata> = {};

    for (const [key, value] of Object.entries(metadata)) {
      if (value === undefined) {
        continue; // Skip undefined values
      }
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        converted[key] = value;
      } else if (value === null) {
        converted[key] = 'null';
      } else {
        converted[key] = String(value);
      }
    }

    return converted;
  }

  /**
   * Convert KV metadata to IMetadata format
   */
  private convertKVMetadataToIMetadata(
    kvMetadata: import('./kv-document-service').IDocumentMetadata
  ): IMetadata {
    return {
      collection: kvMetadata.collection,
      category: kvMetadata.category,
      content_hash: kvMetadata.content_hash,
      file_path: kvMetadata.file_path,
      title: kvMetadata.title,
      created_at: kvMetadata.created_at,
      updated_at: kvMetadata.updated_at,
    };
  }

  /**
   * Lists document IDs with filters using KV storage for metadata-only operations
   * This replaces dummy embedding queries with efficient KV-based filtering
   */
  private async listDocumentIdsWithFilters(params: {
    collection?: string;
    category?: string;
    userID?: string;
    projectID?: string;
    tenantID?: string;
  }): Promise<string[]> {
    // Use KV storage for efficient metadata-only filtering
    if (params.collection) {
      return await this.kvDocumentService.listDocumentsByCollection(params.collection, 1000);
    }

    if (params.category) {
      return await this.kvDocumentService.listDocumentsByCategory(params.category, 1000);
    }

    // For other filters or no filters, get all document IDs and filter in memory
    // This is more efficient than dummy embedding queries
    const { getKV } = await import('@utils');
    const kvInstance = await getKV();
    const prefix = 'document:';

    const listResult = await kvInstance.list({ prefix, limit: 1000 });
    const documentIds: string[] = [];

    for (const key of listResult.keys) {
      const documentId = key.name.replace(prefix, '');

      // Apply tenant filtering if needed
      if (params.userID || params.projectID || params.tenantID) {
        // For tenant filtering, we need to check the metadata
        // This is still more efficient than dummy embedding queries
        const metadata = key.metadata as Record<string, string> | undefined;
        if (params.userID && metadata?.userID !== params.userID) continue;
        if (params.projectID && metadata?.projectID !== params.projectID) continue;
        if (params.tenantID && metadata?.tenantID !== params.tenantID) continue;
      }

      documentIds.push(documentId);
    }

    return documentIds;
  }
}

// Lazy singleton instance
let documentServiceInstance: DocumentService | null = null;

/**
 * Get or create document service instance (lazy initialization)
 */
export const getDocumentService = (): DocumentService => {
  if (!documentServiceInstance) {
    documentServiceInstance = new DocumentService();
  }
  return documentServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const documentService = new Proxy({} as DocumentService, {
  get(_target, prop) {
    const service = getDocumentService();
    const value = service[prop as keyof DocumentService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
