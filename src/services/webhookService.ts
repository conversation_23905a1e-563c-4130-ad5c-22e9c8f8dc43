/**
 * Webhook Service
 *
 * Handles webhook notifications for API key expiration and rotation events.
 * Supports delivery, retries, and webhook management.
 */

import type { IWebhookNotification, IWebhookResult, TEmailTemplate } from '@/types/email';
import type { TPermission } from '@/types/teams';

// ============================================================================
// Webhook Service Class
// ============================================================================

export class WebhookService {
  /**
   * Send webhook notification
   */
  async sendWebhook(
    webhookUrl: string,
    notification: IWebhookNotification,
    options: {
      timeout?: number;
      retryCount?: number;
      headers?: Record<string, string>;
    } = {}
  ): Promise<IWebhookResult> {
    const startTime = Date.now();
    const { timeout = 30000, headers = {} } = options;

    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'EZContext-Webhook/1.0',
          'X-EZContext-Event': notification.event,
          'X-EZContext-Notification-ID': notification.notificationId,
          'X-EZContext-Retry-Count': (options.retryCount || 0).toString(),
          ...headers,
        },
        body: JSON.stringify(notification),
        signal: AbortSignal.timeout(timeout),
      });

      const responseTime = Date.now() - startTime;
      const responseBody = await response.text();

      return {
        success: response.ok,
        statusCode: response.status,
        responseBody: responseBody.substring(0, 1000), // Limit response body size
        deliveredAt: new Date(),
        responseTime,
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        deliveredAt: new Date(),
        responseTime,
      };
    }
  }

  /**
   * Create webhook notification payload
   */
  createWebhookNotification(
    event: TEmailTemplate,
    apiKeyData: {
      id: string;
      name: string;
      prefix: string;
      expiresAt?: Date;
      permissions?: TPermission[];
    },
    userData: {
      id: string;
      email: string;
      name: string;
    },
    teamData?: {
      id: string;
      name: string;
      slug: string;
    },
    eventData: Record<string, unknown> = {},
    retryCount = 0
  ): IWebhookNotification {
    return {
      event,
      timestamp: new Date(),
      apiKey: apiKeyData,
      user: userData,
      team: teamData,
      data: eventData,
      notificationId: crypto.randomUUID(),
      retryCount,
    };
  }

  /**
   * Send API key expiration webhook
   */
  async sendExpirationWebhook(
    webhookUrl: string,
    apiKeyData: {
      id: string;
      name: string;
      prefix: string;
      expiresAt: Date;
      permissions?: TPermission[];
    },
    userData: {
      id: string;
      email: string;
      name: string;
    },
    daysUntilExpiration: number,
    teamData?: {
      id: string;
      name: string;
      slug: string;
    },
    retryCount = 0
  ): Promise<IWebhookResult> {
    const event: TEmailTemplate =
      daysUntilExpiration === 1 ? 'api_key_expiring_1_day' : 'api_key_expiring_7_days';

    const notification = this.createWebhookNotification(
      event,
      apiKeyData,
      userData,
      teamData,
      {
        daysUntilExpiration,
        expirationDate: apiKeyData.expiresAt.toISOString(),
        urgency: daysUntilExpiration <= 1 ? 'high' : 'medium',
      },
      retryCount
    );

    return this.sendWebhook(webhookUrl, notification, { retryCount });
  }

  /**
   * Send API key rotation webhook
   */
  async sendRotationWebhook(
    webhookUrl: string,
    oldApiKeyData: {
      id: string;
      name: string;
      prefix: string;
    },
    newApiKeyData: {
      id: string;
      name: string;
      prefix: string;
      expiresAt?: Date;
    },
    userData: {
      id: string;
      email: string;
      name: string;
    },
    teamData?: {
      id: string;
      name: string;
      slug: string;
    },
    rotationData: {
      rotationType: string;
      reason: string;
      rotatedAt: Date;
      overlapPeriodHours?: number;
    } = {
      rotationType: 'manual',
      reason: 'Manual rotation',
      rotatedAt: new Date(),
    },
    retryCount = 0
  ): Promise<IWebhookResult> {
    const notification = this.createWebhookNotification(
      'api_key_rotated',
      {
        id: newApiKeyData.id,
        name: newApiKeyData.name,
        prefix: newApiKeyData.prefix,
        expiresAt: newApiKeyData.expiresAt,
      },
      userData,
      teamData,
      {
        oldApiKey: oldApiKeyData,
        newApiKey: newApiKeyData,
        rotation: rotationData,
      },
      retryCount
    );

    return this.sendWebhook(webhookUrl, notification, { retryCount });
  }

  /**
   * Send bulk expiration webhook for team administrators
   */
  async sendBulkExpirationWebhook(
    webhookUrl: string,
    teamData: {
      id: string;
      name: string;
      slug: string;
    },
    adminData: {
      id: string;
      email: string;
      name: string;
    },
    expiringKeys: Array<{
      id: string;
      name: string;
      prefix: string;
      expiresAt: Date;
      ownerName: string;
      ownerEmail: string;
    }>,
    retryCount = 0
  ): Promise<IWebhookResult> {
    const notification = this.createWebhookNotification(
      'bulk_expiration_warning',
      {
        id: 'bulk',
        name: 'Bulk Expiration Warning',
        prefix: 'bulk',
      },
      adminData,
      teamData,
      {
        expiringKeysCount: expiringKeys.length,
        expiringKeys: expiringKeys.map((key) => ({
          id: key.id,
          name: key.name,
          prefix: key.prefix,
          expiresAt: key.expiresAt.toISOString(),
          owner: {
            name: key.ownerName,
            email: key.ownerEmail,
          },
        })),
        urgency: expiringKeys.some(
          (key) => Math.ceil((key.expiresAt.getTime() - Date.now()) / (24 * 60 * 60 * 1000)) <= 1
        )
          ? 'high'
          : 'medium',
      },
      retryCount
    );

    return this.sendWebhook(webhookUrl, notification, { retryCount });
  }

  /**
   * Validate webhook URL
   */
  async validateWebhookUrl(webhookUrl: string): Promise<{
    valid: boolean;
    error?: string;
    responseTime?: number;
  }> {
    try {
      const _startTime = Date.now();

      // Send a test ping
      const testNotification = this.createWebhookNotification(
        'api_key_expiring_7_days',
        {
          id: 'test',
          name: 'Test API Key',
          prefix: 'test',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
        {
          id: 'test-user',
          email: '<EMAIL>',
          name: 'Test User',
        },
        undefined,
        {
          test: true,
          message: 'This is a test webhook notification from EZContext',
        }
      );

      const result = await this.sendWebhook(webhookUrl, testNotification, {
        timeout: 10000, // Shorter timeout for validation
        headers: {
          'X-EZContext-Test': 'true',
        },
      });

      return {
        valid: result.success,
        error: result.error,
        responseTime: result.responseTime,
      };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Process webhook with retry logic
   */
  async processWebhookWithRetry(
    webhookUrl: string,
    notification: IWebhookNotification,
    maxRetries = 3
  ): Promise<{
    success: boolean;
    attempts: number;
    lastResult: IWebhookResult;
    allResults: IWebhookResult[];
  }> {
    const results: IWebhookResult[] = [];
    let attempts = 0;

    for (let retry = 0; retry <= maxRetries; retry++) {
      attempts++;

      // Exponential backoff delay
      if (retry > 0) {
        const delay = Math.min(1000 * 2 ** (retry - 1), 30000); // Max 30 seconds
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      const result = await this.sendWebhook(webhookUrl, notification, {
        retryCount: retry,
      });

      results.push(result);

      if (result.success) {
        return {
          success: true,
          attempts,
          lastResult: result,
          allResults: results,
        };
      }

      // Don't retry on client errors (4xx)
      if (result.statusCode && result.statusCode >= 400 && result.statusCode < 500) {
        break;
      }
    }

    return {
      success: false,
      attempts,
      lastResult: results[results.length - 1],
      allResults: results,
    };
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let webhookServiceInstance: WebhookService | null = null;

/**
 * Get or create webhook service instance (lazy initialization)
 */
export const getWebhookService = (): WebhookService => {
  if (!webhookServiceInstance) {
    webhookServiceInstance = new WebhookService();
  }
  return webhookServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const webhookService = new Proxy({} as WebhookService, {
  get(_target, prop) {
    const service = getWebhookService();
    const value = service[prop as keyof WebhookService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
