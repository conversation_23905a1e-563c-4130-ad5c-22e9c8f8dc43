/**
 * Email Service
 *
 * Comprehensive email service with Resend integration for API key
 * expiration and rotation notifications. Supports templates, queuing,
 * and webhook notifications.
 */

import { getEnv } from '@utils';
import type { IApiKeyEmailContext, IEmailConfig, IEmailMessage, IEmailResult } from '@/types/email';

// ============================================================================
// Email Service Class
// ============================================================================

export class EmailService {
  private config: IEmailConfig | null = null;

  /**
   * Initialize email service configuration
   */
  async getConfig(): Promise<IEmailConfig> {
    if (this.config) return this.config;

    const env = await getEnv();

    this.config = {
      provider: 'resend',
      apiKey: env.RESEND_API_KEY || '',
      fromEmail: env.FROM_NAME
        ? `${env.FROM_NAME} <${env.FROM_EMAIL || '<EMAIL>'}>`
        : env.FROM_EMAIL || '<EMAIL>',
      supportEmail: env.SUPPORT_EMAIL || '<EMAIL>',
      baseUrl: env.BASE_URL || 'http://localhost:8787',
    };

    return this.config;
  }

  /**
   * Send email using Resend API
   */
  async sendEmail(message: IEmailMessage): Promise<IEmailResult> {
    try {
      const config = await this.getConfig();

      if (!config.apiKey) {
        throw new Error('Resend API key not configured');
      }

      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: message.from || config.fromEmail,
          to: Array.isArray(message.to) ? message.to : [message.to],
          subject: message.subject,
          html: message.html,
          text: message.text,
          reply_to: message.replyTo,
          cc: message.cc,
          bcc: message.bcc,
          attachments: message.attachments,
          headers: message.headers,
          tags: message.tags,
        }),
      });

      const result = (await response.json()) as { id?: string; message?: string };

      if (!response.ok) {
        throw new Error(`Resend API error: ${result.message || 'Unknown error'}`);
      }

      return {
        success: true,
        messageId: result.id,
        provider: 'resend',
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('Email sending failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        provider: 'resend',
        timestamp: new Date(),
      };
    }
  }

  /**
   * Send API key expiration warning email
   */
  async sendExpirationWarning(
    userEmail: string,
    userName: string,
    context: IApiKeyEmailContext,
    daysUntilExpiration: number
  ): Promise<IEmailResult>;
  async sendExpirationWarning(
    contextOrEmail: IApiKeyEmailContext | string,
    daysOrName?: number | string,
    contextOrUndefined?: IApiKeyEmailContext,
    daysUntilExpiration?: number
  ): Promise<IEmailResult> {
    // Handle both method signatures for backward compatibility
    let context: IApiKeyEmailContext;
    let days: number;

    if (typeof contextOrEmail === 'string') {
      // New signature: (userEmail, userName, context, days)
      if (!contextOrUndefined) {
        throw new Error('Context is required when using string email parameter');
      }
      if (daysUntilExpiration === undefined) {
        throw new Error('Days until expiration is required when using string email parameter');
      }
      context = contextOrUndefined;
      days = daysUntilExpiration;
    } else {
      // Old signature: (context, days)
      context = contextOrEmail;
      days = daysOrName as number;
    }
    const config = await this.getConfig();
    const template = this.getExpirationWarningTemplate(context, days);

    const message: IEmailMessage = {
      to: context.userEmail,
      from: config.fromEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tags: [
        { name: 'category', value: 'api_key_expiration' },
        { name: 'days_until_expiration', value: days.toString() },
        { name: 'key_id', value: context.keyId },
      ],
    };

    return this.sendEmail(message);
  }

  /**
   * Send API key rotation notification email
   */
  async sendRotationNotification(
    userEmail: string,
    userName: string,
    context: IApiKeyEmailContext
  ): Promise<IEmailResult>;
  async sendRotationNotification(
    contextOrEmail: IApiKeyEmailContext | string,
    _userNameOrUndefined?: string,
    contextOrUndefined?: IApiKeyEmailContext
  ): Promise<IEmailResult> {
    // Handle both method signatures for backward compatibility
    let context: IApiKeyEmailContext;

    if (typeof contextOrEmail === 'string') {
      // New signature: (userEmail, userName, context)
      context = contextOrUndefined as IApiKeyEmailContext;
    } else {
      // Old signature: (context)
      context = contextOrEmail;
    }
    const config = await this.getConfig();
    const template = this.getRotationNotificationTemplate(context);

    const message: IEmailMessage = {
      to: context.userEmail,
      from: config.fromEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tags: [
        { name: 'category', value: 'api_key_rotation' },
        { name: 'key_id', value: context.keyId },
      ],
    };

    return this.sendEmail(message);
  }

  /**
   * Send bulk expiration warning to team administrators
   */
  async sendBulkExpirationWarning(
    adminEmail: string,
    teamName: string,
    expiringKeys: Array<{
      name: string;
      prefix: string;
      expiresAt: Date;
      ownerName: string;
    }>
  ): Promise<IEmailResult> {
    const config = await this.getConfig();
    const template = this.getBulkExpirationTemplate(teamName, expiringKeys);

    const message: IEmailMessage = {
      to: adminEmail,
      from: config.fromEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tags: [
        { name: 'category', value: 'bulk_expiration' },
        { name: 'team', value: teamName },
        { name: 'expiring_count', value: expiringKeys.length.toString() },
      ],
    };

    return this.sendEmail(message);
  }

  /**
   * Send bulk expiration summary (alias for sendBulkExpirationWarning)
   */
  async sendBulkExpirationSummary(
    adminEmail: string,
    _adminName: string,
    expiringKeys: Array<{
      name: string;
      prefix: string;
      expiresAt: Date;
      ownerName: string;
    }>
  ): Promise<IEmailResult> {
    // Use the team name as "Team" for now since we don't have team context
    return this.sendBulkExpirationWarning(adminEmail, 'Team', expiringKeys);
  }

  // ============================================================================
  // Template Generation Methods
  // ============================================================================

  /**
   * Generate expiration warning email template
   */
  private getExpirationWarningTemplate(
    context: IApiKeyEmailContext,
    daysUntilExpiration: number
  ): { subject: string; html: string; text: string } {
    const urgencyLevel = daysUntilExpiration <= 1 ? 'urgent' : 'warning';
    const timeText = daysUntilExpiration === 1 ? '1 day' : `${daysUntilExpiration} days`;

    const subject = `${urgencyLevel === 'urgent' ? '🚨 URGENT:' : '⚠️'} API Key "${context.keyName}" expires in ${timeText}`;

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>API Key Expiration Warning</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: ${urgencyLevel === 'urgent' ? '#dc3545' : '#ffc107'}; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
            .key-info { background: white; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .button.danger { background: #dc3545; }
            .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>${urgencyLevel === 'urgent' ? '🚨 URGENT' : '⚠️ WARNING'}: API Key Expiring Soon</h1>
            </div>
            <div class="content">
              <p>Hello ${context.userName},</p>
              
              <p>Your API key is scheduled to expire in <strong>${timeText}</strong>.</p>
              
              <div class="key-info">
                <h3>API Key Details</h3>
                <ul>
                  <li><strong>Name:</strong> ${context.keyName}</li>
                  <li><strong>Key ID:</strong> ${context.keyPrefix}...</li>
                  <li><strong>Expires:</strong> ${context.expiresAt?.toLocaleDateString() || 'No expiration set'}</li>
                  ${context.teamName ? `<li><strong>Team:</strong> ${context.teamName}</li>` : ''}
                  ${context.usageStats ? `<li><strong>Monthly Usage:</strong> ${context.usageStats.monthlyUsage} requests</li>` : ''}
                </ul>
              </div>
              
              <p><strong>Action Required:</strong> To prevent service interruption, please take one of the following actions:</p>
              
              <div style="text-align: center; margin: 20px 0;">
                <a href="${context.rotateUrl}" class="button">🔄 Rotate Key Now</a>
                <a href="${context.manageUrl}" class="button">⚙️ Manage Key</a>
              </div>
              
              <p><strong>What happens if the key expires?</strong></p>
              <ul>
                <li>All API requests using this key will be rejected</li>
                <li>Applications using this key will stop working</li>
                <li>You'll need to update your applications with a new key</li>
              </ul>
              
              <p>If you have any questions, please contact our support team.</p>
            </div>
            <div class="footer">
              <p>This is an automated notification from EZContext API Key Management System.</p>
              <p>If you no longer wish to receive these notifications, you can update your preferences in your account settings.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
API Key Expiration Warning

Hello ${context.userName},

Your API key "${context.keyName}" (${context.keyPrefix}...) is scheduled to expire in ${timeText}.

Expiration Date: ${context.expiresAt?.toLocaleDateString() || 'No expiration set'}
${context.teamName ? `Team: ${context.teamName}` : ''}

Action Required:
To prevent service interruption, please rotate or manage your API key:

Rotate Key: ${context.rotateUrl}
Manage Key: ${context.manageUrl}

What happens if the key expires?
- All API requests using this key will be rejected
- Applications using this key will stop working
- You'll need to update your applications with a new key

If you have any questions, please contact our support team.

---
This is an automated notification from EZContext API Key Management System.
    `;

    return { subject, html, text };
  }

  /**
   * Generate rotation notification email template
   */
  private getRotationNotificationTemplate(context: IApiKeyEmailContext): {
    subject: string;
    html: string;
    text: string;
  } {
    const subject = `✅ API Key "${context.keyName}" has been rotated successfully`;

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>API Key Rotated Successfully</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
            .key-info { background: white; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ API Key Rotated Successfully</h1>
            </div>
            <div class="content">
              <p>Hello ${context.userName},</p>
              
              <p>Your API key has been successfully rotated. A new key has been generated and is ready for use.</p>
              
              <div class="key-info">
                <h3>Rotation Details</h3>
                <ul>
                  <li><strong>Key Name:</strong> ${context.keyName}</li>
                  <li><strong>New Key Prefix:</strong> ${context.newKeyPrefix}...</li>
                  <li><strong>Rotated At:</strong> ${context.rotatedAt?.toLocaleString()}</li>
                  ${context.teamName ? `<li><strong>Team:</strong> ${context.teamName}</li>` : ''}
                  ${context.expiresAt ? `<li><strong>New Expiration:</strong> ${context.expiresAt.toLocaleDateString()}</li>` : ''}
                </ul>
              </div>
              
              <div class="warning">
                <h4>⚠️ Important: Update Your Applications</h4>
                <p>The old API key has been deactivated. You must update all applications and services to use the new key to avoid service interruption.</p>
              </div>
              
              <div style="text-align: center; margin: 20px 0;">
                <a href="${context.manageUrl}" class="button">📋 View New Key</a>
                ${context.teamUrl ? `<a href="${context.teamUrl}" class="button">👥 Team Dashboard</a>` : ''}
              </div>
              
              <p><strong>Next Steps:</strong></p>
              <ol>
                <li>Copy the new API key from your dashboard</li>
                <li>Update all applications and services</li>
                <li>Test your integrations to ensure they're working</li>
                <li>Remove any references to the old key</li>
              </ol>
              
              <p>If you didn't request this rotation or have any concerns, please contact our support team immediately.</p>
            </div>
            <div class="footer">
              <p>This is an automated notification from EZContext API Key Management System.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
API Key Rotated Successfully

Hello ${context.userName},

Your API key "${context.keyName}" has been successfully rotated.

Rotation Details:
- New Key Prefix: ${context.newKeyPrefix}...
- Rotated At: ${context.rotatedAt?.toLocaleString()}
${context.teamName ? `- Team: ${context.teamName}` : ''}
${context.expiresAt ? `- New Expiration: ${context.expiresAt.toLocaleDateString()}` : ''}

IMPORTANT: Update Your Applications
The old API key has been deactivated. You must update all applications and services to use the new key to avoid service interruption.

View New Key: ${context.manageUrl}

Next Steps:
1. Copy the new API key from your dashboard
2. Update all applications and services
3. Test your integrations to ensure they're working
4. Remove any references to the old key

If you didn't request this rotation or have any concerns, please contact our support team immediately.

---
This is an automated notification from EZContext API Key Management System.
    `;

    return { subject, html, text };
  }

  /**
   * Generate bulk expiration warning template for team administrators
   */
  private getBulkExpirationTemplate(
    teamName: string,
    expiringKeys: Array<{
      name: string;
      prefix: string;
      expiresAt: Date;
      ownerName: string;
    }>
  ): { subject: string; html: string; text: string } {
    const subject = `⚠️ ${expiringKeys.length} API keys expiring soon in team "${teamName}"`;

    const keysList = expiringKeys
      .map(
        (key) => `
        <tr>
          <td>${key.name}</td>
          <td>${key.prefix}...</td>
          <td>${key.ownerName}</td>
          <td>${key.expiresAt.toLocaleDateString()}</td>
        </tr>
      `
      )
      .join('');

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Team API Keys Expiring Soon</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 700px; margin: 0 auto; padding: 20px; }
            .header { background: #ffc107; color: #212529; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
            table { width: 100%; border-collapse: collapse; margin: 15px 0; background: white; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
            th { background: #e9ecef; font-weight: bold; }
            .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>⚠️ Team API Keys Expiring Soon</h1>
            </div>
            <div class="content">
              <p>Hello Team Administrator,</p>
              
              <p>This is a summary of API keys in team "<strong>${teamName}</strong>" that are scheduled to expire soon.</p>
              
              <h3>Expiring API Keys (${expiringKeys.length})</h3>
              <table>
                <thead>
                  <tr>
                    <th>Key Name</th>
                    <th>Key Prefix</th>
                    <th>Owner</th>
                    <th>Expires</th>
                  </tr>
                </thead>
                <tbody>
                  ${keysList}
                </tbody>
              </table>
              
              <p><strong>Recommended Actions:</strong></p>
              <ul>
                <li>Contact key owners to rotate their keys</li>
                <li>Review team API key policies</li>
                <li>Consider implementing automatic rotation</li>
                <li>Update team notification preferences if needed</li>
              </ul>
              
              <div style="text-align: center; margin: 20px 0;">
                <a href="${teamName}" class="button">👥 Manage Team Keys</a>
              </div>
              
              <p>Individual key owners have also been notified about their expiring keys.</p>
            </div>
            <div class="footer">
              <p>This is an automated notification from EZContext API Key Management System.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const keysText = expiringKeys
      .map(
        (key) =>
          `- ${key.name} (${key.prefix}...) - Owner: ${key.ownerName} - Expires: ${key.expiresAt.toLocaleDateString()}`
      )
      .join('\n');

    const text = `
Team API Keys Expiring Soon

Hello Team Administrator,

This is a summary of API keys in team "${teamName}" that are scheduled to expire soon.

Expiring API Keys (${expiringKeys.length}):
${keysText}

Recommended Actions:
- Contact key owners to rotate their keys
- Review team API key policies
- Consider implementing automatic rotation
- Update team notification preferences if needed

Individual key owners have also been notified about their expiring keys.

---
This is an automated notification from EZContext API Key Management System.
    `;

    return { subject, html, text };
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(
    userEmail: string,
    userName: string,
    resetUrl: string
  ): Promise<IEmailResult> {
    const { subject, html, text } = this.generatePasswordResetTemplate(userName, resetUrl);

    const message: IEmailMessage = {
      to: userEmail,
      from: '<EMAIL>',
      subject,
      html,
      text,
    };

    return this.sendEmail(message);
  }

  /**
   * Generate password reset email template
   */
  private generatePasswordResetTemplate(userName: string, resetUrl: string) {
    const subject = 'Reset Your EZContext Password';

    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>${subject}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
    .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 14px; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Password Reset Request</h1>
    </div>

    <p>Hello ${userName},</p>

    <p>We received a request to reset your password for your EZContext account. If you made this request, click the button below to reset your password:</p>

    <a href="${resetUrl}" class="button">Reset Password</a>

    <p>This link will expire in 1 hour for security reasons.</p>

    <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>

    <div class="footer">
      <p>This is an automated message from EZContext. Please do not reply to this email.</p>
      <p>If you need help, contact <NAME_EMAIL></p>
    </div>
  </div>
</body>
</html>
    `;

    const text = `
Password Reset Request

Hello ${userName},

We received a request to reset your password for your EZContext account. If you made this request, use the link below to reset your password:

${resetUrl}

This link will expire in 1 hour for security reasons.

If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.

---
This is an automated message from EZContext. Please do not reply to this email.
If you need help, contact <NAME_EMAIL>
    `;

    return { subject, html, text };
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let emailServiceInstance: EmailService | null = null;

/**
 * Get or create email service instance (lazy initialization)
 */
export const getEmailService = (): EmailService => {
  if (!emailServiceInstance) {
    emailServiceInstance = new EmailService();
  }
  return emailServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const emailService = new Proxy({} as EmailService, {
  get(_target, prop) {
    const service = getEmailService();
    const value = service[prop as keyof EmailService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  }
});
