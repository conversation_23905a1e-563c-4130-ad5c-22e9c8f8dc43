{"name": "ezcontext-n", "scripts": {"dev": "wrangler dev --remote", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "seed": "tsx scripts/seed-vectordb.ts", "test": "node scripts/api-test.js", "test:run": "node scripts/api-test.js", "test:verbose": "VERBOSE=true node scripts/api-test.js", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format .", "format:fix": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write ."}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@hono/zod-validator": "^0.7.0", "@modelcontextprotocol/sdk": "^1.15.0", "@neondatabase/serverless": "^1.0.1", "@paddle/paddle-node-sdk": "^3.0.0", "@paypal/paypal-server-sdk": "^1.1.0", "ai": "^4.3.16", "better-auth": "^1.2.12", "drizzle-orm": "^0.44.2", "hono": "^4.8.4", "openai": "^5.8.2", "unpdf": "^1.0.6", "voyageai": "^0.0.4", "zod": "^3.25.74"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^24.0.10", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3", "typescript": "^5.8.3", "wrangler": "^4.4.0"}}